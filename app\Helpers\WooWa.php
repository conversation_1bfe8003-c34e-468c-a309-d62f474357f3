<?php

namespace App\Helpers;

use App\Models\ApiKey;
use App\Models\CsWoowaConfiguration;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class WooWa
{
    public static function sendMessage($phone_no, $message, $url_params, $cs_name = null)
    {
        try {
            // Get configuration from CS-specific table
            $config = null;

            // If CS name is provided, find the specific CS configuration
            if ($cs_name) {
                $user = User::where('name', $cs_name)
                    ->where('level_user', 3) // Only CS users
                    ->first();

                if ($user) {
                    $config = CsWoowaConfiguration::where('user_id', $user->id)
                        ->where('is_active', true)
                        ->first();
                }
            }

            // If no CS-specific config found, try to get from general API keys (fallback)
            if (!$config || !$config->isComplete()) {
                Log::info('Using fallback API key configuration', [
                    'cs_name' => $cs_name,
                    'config_found' => $config ? 'incomplete' : 'not_found'
                ]);

                $generalConfig = ApiKey::getConfig();

                if (empty($generalConfig->woowa_api_key) || empty($generalConfig->woowa_base_url)) {
                    Log::warning('No WooWa configuration available', [
                        'cs_name' => $cs_name,
                        'general_api_key_empty' => empty($generalConfig->woowa_api_key),
                        'general_base_url_empty' => empty($generalConfig->woowa_base_url)
                    ]);

                    // Final fallback to hardcoded values
                    $key = 'adpfe27167c-53b6-49c7-8d33-8ca916a3e58a';
                    $base_url = 'https://notifapi.com';
                } else {
                    $key = $generalConfig->woowa_api_key;
                    $base_url = rtrim($generalConfig->woowa_base_url, '/');
                }
            } else {
                // Use CS-specific configuration
                $key = $config->woowa_api_key;
                $base_url = rtrim($config->woowa_base_url, '/');

                Log::info('Using CS-specific WooWa configuration', [
                    'cs_name' => $cs_name,
                    'user_id' => $config->user_id,
                    'config_id' => $config->id
                ]);
            }

            $url = $base_url . '/' . $url_params;
            $data = [
                "phone_no" => $phone_no,
                "key" => $key,
                "message" => $message
            ];

            $data_string = json_encode($data, JSON_UNESCAPED_UNICODE);

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_VERBOSE, 0);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
            ]);

            $res = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            if ($curl_error) {
                Log::error('WooWa cURL error: ' . $curl_error, [
                    'cs_name' => $cs_name,
                    'phone_no' => $phone_no,
                    'url' => $url
                ]);
            }

            // Log successful/failed sending
            Log::info('WooWa message sent', [
                'cs_name' => $cs_name,
                'phone_no' => $phone_no,
                'http_code' => $httpCode,
                'success' => $httpCode === 200,
                'config_source' => $config ? 'cs_specific' : 'fallback'
            ]);

            return $res;

        } catch (\Exception $e) {
            Log::error('WooWa sendMessage error: ' . $e->getMessage(), [
                'cs_name' => $cs_name,
                'phone_no' => $phone_no,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get WooWa configuration for specific CS
     */
    public static function getConfigForCS($cs_name)
    {
        try {
            $user = User::where('name', $cs_name)
                ->where('level_user', 3)
                ->first();

            if (!$user) {
                return null;
            }

            return CsWoowaConfiguration::where('user_id', $user->id)
                ->where('is_active', true)
                ->first();
        } catch (\Exception $e) {
            Log::error('Error getting CS WooWa config: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if CS has valid WooWa configuration
     */
    public static function isCSConfigured($cs_name)
    {
        $config = self::getConfigForCS($cs_name);
        return $config && $config->isComplete();
    }

    /**
     * Get all CS with WooWa configuration
     */
    public static function getConfiguredCS()
    {
        try {
            return CsWoowaConfiguration::with('user')
                ->where('is_active', true)
                ->whereNotNull('woowa_api_key')
                ->whereNotNull('woowa_phone_num')
                ->whereNotNull('woowa_base_url')
                ->get()
                ->map(function ($config) {
                    return [
                        'cs_name' => $config->user->name,
                        'phone_formatted' => $config->formatted_phone,
                        'last_tested' => $config->last_tested_at?->format('d/m/Y H:i'),
                        'status' => $config->isLastTestSuccessful() ? 'OK' : 'Error'
                    ];
                });
        } catch (\Exception $e) {
            Log::error('Error getting configured CS list: ' . $e->getMessage());
            return collect();
        }
    }
}
