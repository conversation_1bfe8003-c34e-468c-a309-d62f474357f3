<?php

namespace App\Enums\Order;
enum PackageType: int
{
    case ELECTRONICS_GADGETS = 1;
    case CLOTHING = 2;
    case FRAGILE = 3;
    case DOCUMENTS = 4;
    case HOUSEHOLD_EQUIPMENT = 5;
    case ACCESSORIES = 6;
    case OTHERS = 7;
    case VALUABLE_DOCUMENTS = 8;
    case HEALTH_BEAUTY = 9;
    case SPORTS_ENTERTAINMENT = 10;
    case CAR_MOTORCYCLE = 11;

    public function label(): string
    {
        return match ($this) {
            self::ELECTRONICS_GADGETS => 'Peralatan Elektronik & Gadget',
            self::CLOTHING => 'Pakaian',
            self::FRAGILE => 'Pecah <PERSON>ah',
            self::DOCUMENTS => 'Dokumen',
            self::HOUSEHOLD_EQUIPMENT => 'Peralatan Rumah Tangga',
            self::ACCESSORIES => 'Aksesoris',
            self::OTHERS => 'Lain-lain',
            self::VALUABLE_DOCUMENTS => 'Dokumen Berharga',
            self::HEALTH_BEAUTY => 'Peralatan <PERSON> & Kecanti<PERSON>',
            self::SPORTS_ENTERTAINMENT => 'Peralatan Olahraga & Hiburan',
            self::CAR_MOTORCYCLE => 'Perlengkapan Mobil & Motor',
        };
    }

    public function requiresInsurance(): bool
    {
        return match ($this) {
            self::ELECTRONICS_GADGETS,
            self::FRAGILE,
            self::DOCUMENTS,
            self::VALUABLE_DOCUMENTS => true,
            default => false,
        };
    }

    public function insuranceStatus(): string
    {
        return $this->requiresInsurance() ? 'Wajib' : 'Opsional';
    }

    /**
     * Convert all enum cases into an array.
     *
     * @return array
     */
    public static function toArray(): array
    {
        return array_map(fn($case) => [
            'id' => $case->value,
            'label' => $case->label(),
            'requires_insurance' => $case->requiresInsurance(),
            'insurance_status' => $case->insuranceStatus(),
        ], self::cases());
    }

    public static function search(?string $term = null): array
    {
        $cases = self::cases();

        if($term) {
            $cases = array_filter($cases, function($case) use ($term) {
                return stripos($case->label(), $term) !== false;
            });
        }

        return array_map(function($case) {
            return [
                'id' => $case->value,
                'text' => $case->label(),
                'requires_insurance' => $case->requiresInsurance(),
                'insurance_status' => $case->insuranceStatus(),
            ];
        }, $cases);
    }


}
