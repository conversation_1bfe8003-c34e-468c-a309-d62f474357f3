<script>
    $(document).ready(function() {
        // Track both existing and new images
        let existingImages = [];
        let newImages = [];
        let deletedImageIds = [];
        let primaryImageIndex = null;
        let primaryImageType = 'existing'; // 'existing' or 'new'

        // Load existing images on page load
        @if (isset($product) && $product->images->count() > 0)
            // Populate existing images
            @foreach ($product->images->sortBy('sort_order') as $index => $image)
                existingImages.push({
                    id: {{ $image->id }},
                    url: "{{ $image->url }}",
                    path: "{{ $image->image_path }}",
                    is_primary: {{ $image->is_primary ? 'true' : 'false' }},
                    sort_order: {{ $image->sort_order }}
                });

                @if ($image->is_primary)
                    primaryImageIndex = {{ $index }};
                    primaryImageType = 'existing';
                @endif
            @endforeach

            // Initial render
            updateImagePreviews();
            updateImageInputs();
        @endif

        function updateImagePreviews() {
            try {
                $('#preview-container').empty();

                // Render existing images
                existingImages.forEach((image, index) => {
                    const isPrimary = (primaryImageType === 'existing' && primaryImageIndex === index);
                    const previewHtml = `
                    <div class="image-preview existing-image" data-type="existing" data-id="${image.id}" data-index="${index}">
                        <img src="${image.url}" alt="Preview">
                        ${isPrimary ? '<span class="primary-badge">Utama</span>' : ''}
                        <span class="image-type-badge">Existing</span>
                        <div class="controls">
                            <button type="button" class="set-primary-btn" title="Jadikan Gambar Utama">
                                <i class="bx ${isPrimary ? 'bxs-star' : 'bx-star'}"></i>
                            </button>
                            <button type="button" class="delete-existing-btn" title="Hapus Gambar">
                                <i class="bx bx-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                    $('#preview-container').append(previewHtml);
                });

                // Render new images
                newImages.forEach((image, index) => {
                    const isPrimary = (primaryImageType === 'new' && primaryImageIndex === index);
                    const previewHtml = `
                    <div class="image-preview new-image" data-type="new" data-index="${index}">
                        <img src="${image.preview}" alt="Preview">
                        ${isPrimary ? '<span class="primary-badge">Utama</span>' : ''}
                        <span class="image-type-badge new">Baru</span>
                        <div class="controls">
                            <button type="button" class="set-primary-btn" title="Jadikan Gambar Utama">
                                <i class="bx ${isPrimary ? 'bxs-star' : 'bx-star'}"></i>
                            </button>
                            <button type="button" class="delete-new-btn" title="Hapus Gambar">
                                <i class="bx bx-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                    $('#preview-container').append(previewHtml);
                });

                updateImagesEmptyState();
            } catch (e) {
                console.error('Error updating image previews:', e);
            }
        }

        function updateImagesEmptyState() {
            const totalImages = existingImages.length + newImages.length;
            if (totalImages === 0) {
                $('#empty-images-state').show();
                $('#preview-container').hide();
            } else {
                $('#empty-images-state').hide();
                $('#preview-container').show();
            }
        }

        function updateImageInputs() {
            try {
                $('#all-images-container').empty();

                // Add deleted image IDs
                deletedImageIds.forEach(id => {
                    const input = `<input type="hidden" name="deleted_images[]" value="${id}">`;
                    $('#all-images-container').append(input);
                });

                // Add new images
                newImages.forEach((image, index) => {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.name = 'product_images[]';
                    fileInput.style.display = 'none';

                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(image.file);
                    fileInput.files = dataTransfer.files;

                    $('#all-images-container').append(fileInput);
                });

                // Add primary image info
                if (primaryImageIndex !== null) {
                    const primaryInput = `<input type="hidden" name="primary_image_type" value="${primaryImageType}">
                                     <input type="hidden" name="primary_image_index" value="${primaryImageIndex}">`;
                    $('#all-images-container').append(primaryInput);
                }

                // Update count
                const totalImages = existingImages.length + newImages.length;
                $('#images-count').text(`${totalImages} gambar`);
            } catch (e) {
                console.error('Error updating image inputs:', e);
            }
        }

        // Handle delete existing image
        $('#preview-container').on('click', '.delete-existing-btn', function() {
            try {
                const preview = $(this).closest('.image-preview');
                const imageId = parseInt(preview.data('id'));
                const index = parseInt(preview.data('index'));

                // Add to deleted list
                deletedImageIds.push(imageId);

                // Remove from existing images
                existingImages.splice(index, 1);

                // Adjust primary if needed
                if (primaryImageType === 'existing' && primaryImageIndex === index) {
                    // Set first available image as primary
                    if (existingImages.length > 0) {
                        primaryImageIndex = 0;
                        primaryImageType = 'existing';
                    } else if (newImages.length > 0) {
                        primaryImageIndex = 0;
                        primaryImageType = 'new';
                    } else {
                        primaryImageIndex = null;
                        primaryImageType = 'existing';
                    }
                } else if (primaryImageType === 'existing' && primaryImageIndex > index) {
                    primaryImageIndex--;
                }

                updateImagePreviews();
                updateImageInputs();
            } catch (e) {
                console.error('Error deleting existing image:', e);
            }
        });

        // Handle delete new image
        $('#preview-container').on('click', '.delete-new-btn', function() {
            try {
                const preview = $(this).closest('.image-preview');
                const index = parseInt(preview.data('index'));

                // Remove from new images
                newImages.splice(index, 1);

                // Adjust primary if needed
                if (primaryImageType === 'new' && primaryImageIndex === index) {
                    // Set first available image as primary
                    if (newImages.length > 0) {
                        primaryImageIndex = 0;
                        primaryImageType = 'new';
                    } else if (existingImages.length > 0) {
                        primaryImageIndex = 0;
                        primaryImageType = 'existing';
                    } else {
                        primaryImageIndex = null;
                        primaryImageType = 'existing';
                    }
                } else if (primaryImageType === 'new' && primaryImageIndex > index) {
                    primaryImageIndex--;
                }

                updateImagePreviews();
                updateImageInputs();
            } catch (e) {
                console.error('Error deleting new image:', e);
            }
        });

        // Handle set primary button clicks
        $('#preview-container').on('click', '.set-primary-btn', function() {
            try {
                const preview = $(this).closest('.image-preview');
                const type = preview.data('type');
                const index = parseInt(preview.data('index'));

                // Set as primary
                primaryImageIndex = index;
                primaryImageType = type;

                updateImagePreviews();
                updateImageInputs();
            } catch (e) {
                console.error('Error setting primary image:', e);
            }
        });

        // Initialize Dropzone for new images
        let myDropzone = new Dropzone("#productImages", {
            url: "/", // Dummy URL, we won't be uploading directly
            paramName: "file",
            maxFilesize: 2, // MB
            acceptedFiles: "image/jpeg,image/png,image/jpg",
            addRemoveLinks: false,
            autoProcessQueue: false, // Prevent automatic upload
            createImageThumbnails: true,
            dictDefaultMessage: "Tarik & lepas gambar di sini atau klik untuk memilih file",
            previewsContainer: false, // We'll handle previews ourselves

            // Add file to our collection when added to queue
            addedfile: function(file) {
                try {
                    // Create a temporary preview
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const newImage = {
                            file: file,
                            preview: e.target.result
                        };

                        // Add to new images array
                        newImages.push(newImage);

                        // Set first image as primary by default if no primary is set
                        if (primaryImageIndex === null) {
                            primaryImageIndex = 0;
                            primaryImageType = newImages.length > 0 ? 'new' : 'existing';
                        }

                        // Update the preview
                        updateImagePreviews();
                        updateImageInputs();
                    };
                    reader.readAsDataURL(file);
                } catch (e) {
                    console.error('Error adding new image file:', e);
                }
            }
        });

        // Make the preview container sortable for reordering
        $("#preview-container").sortable({
            placeholder: "image-preview-placeholder",
            cursor: "grabbing",
            update: function(event, ui) {
                try {
                    // Get the new order from DOM
                    const newExistingOrder = [];
                    const newNewOrder = [];

                    $('#preview-container .image-preview').each(function() {
                        const type = $(this).data('type');
                        const index = parseInt($(this).data('index'));

                        if (type === 'existing' && !isNaN(index) && existingImages[index]) {
                            newExistingOrder.push(existingImages[index]);
                        } else if (type === 'new' && !isNaN(index) && newImages[index]) {
                            newNewOrder.push(newImages[index]);
                        }
                    });

                    // Update arrays with new order
                    existingImages = newExistingOrder;
                    newImages = newNewOrder;

                    // Update primary image index after reordering
                    let totalProcessed = 0;
                    if (primaryImageType === 'existing') {
                        // Primary is in existing images, find its new position
                        primaryImageIndex = totalProcessed + existingImages.findIndex(img => img.is_primary);
                        if (primaryImageIndex < totalProcessed) primaryImageIndex = null;
                    } else if (primaryImageType === 'new') {
                        // Primary is in new images, find its new position after existing images
                        totalProcessed += existingImages.length;
                        // We need to track which new image was primary differently since new images don't have is_primary
                        // For now, keep the current logic but this might need refinement
                    }

                    // Re-render with new order
                    updateImagePreviews();
                    updateImageInputs();
                } catch (e) {
                    console.error('Error during sort update:', e);
                }
            }
        });

        // Image preparation function for form submission
        window.prepareProductImages = function() {
            if (newImages.length > 0 || deletedImageIds.length > 0) {
                // This function is called before form submission
                // All necessary inputs are already in the DOM via updateImageInputs()
                console.log('Preparing product images for submission', {
                    existingImages: existingImages.length,
                    newImages: newImages.length,
                    deletedImages: deletedImageIds.length,
                    primaryType: primaryImageType,
                    primaryIndex: primaryImageIndex
                });
            }
        };

        // Expose dropzone to global scope
        window.productDropzone = myDropzone;

        // Initial call to set up empty state
        updateImagesEmptyState();
    });
</script>
