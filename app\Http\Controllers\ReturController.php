<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\Retur\ReturService;
use Yajra\DataTables\Facades\DataTables;

class ReturController extends Controller
{
    public function __construct(protected ReturService $returService)
    {
    }

    public function index()
    {
        return view('retur.index', [
            'title' => 'Retur'
        ]);
    }

    public function data()
    {
        $data = $this->returService->getAll();
        // <th>ORDER ID</th>
        // <th>TANGGAL RETUR</th>
        // <th>PRODUK</th>
        // <th>KODE PRODUK</th>
        // <th>QTY</th>
        // <th>METODE PEMBAYARAN</th>
        // <th>KURIR</th>
        // <th>NO RESI</th>
        // <th>STATUS</th>
        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('order_id', fn($row) => $row->kode_pesanan ?? 'ID Tidak Ditemukan')
            ->addColumn('returned_at', fn($row) => $row->tanggal_retur ? $row->tanggal_retur->format('d-m-Y H:i:s') : null)
            ->addColumn('product', fn($row) => $row->nama_produk ?? 'Produk Tidak Ditemukan')
            ->addColumn('product_code', fn($row) => $row->kode_produk ?? 'Kode Produk Tidak Ditemukan')
            ->addColumn('quantity', fn($row) => $row->jumlah ?? '0')
            ->addColumn('payment_method', fn($row) => view('components.payment-method-badge', ['method' => $row->metode_pembayaran]))
            ->addColumn('courier', fn($row) => $row->kurir ?? 'Kurir Tidak Ditemukan')
            ->addColumn('receipt_number', fn($row) => $row->nomor_resi ?? 'Nomor Resi Tidak Ditemukan')
            ->addColumn('status', fn($row) => view('components.kelayakan-badge', ['status' => $row->status]))
            ->rawColumns(['status', 'payment_method'])
            ->make(true);
    }

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        //
    }

    public function show(string $id)
    {
        //
    }

    public function edit(string $id)
    {
        //
    }

    public function update(string $id, Request $request)
    {
        //
    }

    public function destroy(string $id)
    {
        //
    }
}
