<style>

/* Base Styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    overflow-x: hidden;
}

.section {
    min-height: 100vh;
    padding: 4rem 0;
    display: flex;
    align-items: center;
}

.section-content {
    width: 100%;
}

.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #333;
    line-height: 1.2;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.4;
}

.section-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #555;
    line-height: 1.6;
}

/* Section 1 specific styling */
.section-1 .section-title,
.section-1 .section-subtitle,
.section-1 .section-description {
    text-align: center;
}

/* Sections 2-5 specific styling */
.section-content-text .section-title,
.section-content-text .section-subtitle,
.section-content-text .section-description {
    text-align: left;
}

.section-image {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.section-image:hover {
    transform: scale(1.02);
}

.cta-button {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: bold;
    border-radius: 50px;
    color: white;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    color: white;
}

.checkout-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
}

.checkout-form {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    color: #333;
}

.form-control,
.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* Form Section Improvements */
.form-section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: #f8f9ff;
    border-radius: 15px;
    border: 1px solid #e7e7ff;
}

.form-section .section-title {
    font-size: 1.3rem;
    color: #495057;
    margin-bottom: 1.5rem;
    text-align: left;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.order-summary {
    background: linear-gradient(135deg, #f8f9ff 0%, #e7e7ff 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px solid #667eea;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-divider {
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    margin: 1rem 0;
    border-radius: 1px;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    font-size: 1.2rem;
    font-weight: bold;
}

.total-price {
    color: #28a745;
    font-size: 1.4rem;
}

.order-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.order-button {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    padding: 15px;
    font-size: 1.2rem;
    font-weight: bold;
    border-radius: 15px;
    color: white;
    width: 100%;
    transition: all 0.3s ease;
}

.order-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.scroll-button {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    padding: 12px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 12px;
    color: white;
    width: 100%;
    transition: all 0.3s ease;
}

.scroll-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover,
.payment-option.active {
    border-color: #667eea;
    background: #f8f9ff;
}

.product-name {
    font-size: 1.8rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

/* Section separator */
.section-separator {
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
    background-image: repeating-linear-gradient(90deg,
            #007bff,
            #007bff 10px,
            transparent 10px,
            transparent 20px);
    margin: 2rem 0;
    border-radius: 2px;
}

/* Responsive Design - Mobile First Approach */

/* Mobile Styles (up to 576px) */
@media (max-width: 575.98px) {
    .section {
        min-height: auto;
        padding: 2rem 0;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 0.75rem;
    }

    .section-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .section-description {
        font-size: 0.95rem;
        margin-bottom: 1.5rem;
    }

    .checkout-form {
        padding: 1.5rem;
        border-radius: 15px;
    }

    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
    }

    .form-section .section-title {
        font-size: 1.1rem;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 1rem;
        width: 100%;
        text-align: center;
        margin-bottom: 1rem;
    }

    .order-button,
    .scroll-button {
        padding: 12px;
        font-size: 1rem;
    }

    .payment-option {
        padding: 12px;
        margin-bottom: 8px;
    }

    .summary-item {
        padding: 0.5rem 0;
        font-size: 0.9rem;
    }

    .summary-total {
        padding: 0.75rem 0;
        font-size: 1.1rem;
    }

    .total-price {
        font-size: 1.2rem;
    }

    .product-name {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
    }

    /* Stack columns on mobile */
    .row.align-items-center>.col-lg-6 {
        margin-bottom: 2rem;
    }

    .row.align-items-center>.col-lg-6:last-child {
        margin-bottom: 0;
    }
}

/* Small tablets (576px to 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
    .section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }

    .checkout-form {
        padding: 2rem;
    }

    .form-section {
        padding: 1.75rem;
    }

    .cta-button {
        padding: 14px 28px;
        font-size: 1.05rem;
    }
}

/* Medium tablets (768px to 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
    .section {
        padding: 3.5rem 0;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .checkout-form {
        padding: 2.5rem;
    }

    .cta-button {
        padding: 15px 30px;
        font-size: 1.1rem;
    }
}

/* Large tablets and small desktops (992px to 1199.98px) */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .section-title {
        font-size: 2.3rem;
    }

    .checkout-form {
        padding: 2.75rem;
    }
}

/* Extra large screens (1200px and up) */
@media (min-width: 1200px) {
    .section-title {
        font-size: 2.5rem;
    }

    .checkout-form {
        padding: 3rem;
    }
}

/* Landscape mobile orientation */
@media (max-width: 767.98px) and (orientation: landscape) {
    .section {
        min-height: auto;
        padding: 1.5rem 0;
    }

    .section-title {
        font-size: 1.6rem;
    }

    .section-subtitle {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {

    .cta-button,
    .order-button,
    .scroll-button {
        min-height: 44px;
        padding: 12px 20px;
    }

    .payment-option {
        min-height: 44px;
        padding: 12px 15px;
    }

    .form-control,
    .form-select {
        min-height: 44px;
        padding: 12px 15px;
        font-size: 16px;
        /* Prevents zoom on iOS */
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
    .section-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Floating Action Button for Mobile */
.floating-cta {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: none;
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: bold;
    box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
    max-width: calc(100vw - 40px);
    text-align: center;
}

.floating-cta:hover {
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 6px 25px rgba(40, 167, 69, 0.5);
    color: white;
}

.floating-cta.show {
    display: block;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Show floating button only on mobile when not in checkout section */
@media (max-width: 767.98px) {
    .floating-cta {
        display: block;
    }
}

/* Print styles */
@media print {

    .cta-button,
    .order-button,
    .scroll-button,
    .floating-cta {
        display: none;
    }

    .section {
        min-height: auto;
        padding: 1rem 0;
        page-break-inside: avoid;
    }

    .checkout-form {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

</style>
