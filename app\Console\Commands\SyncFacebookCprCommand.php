<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SyncFacebookCprData;
use App\Models\OptimizedMonitoringCpr;
use Illuminate\Support\Facades\Log;

class SyncFacebookCprCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cpr:sync 
                            {--account= : Specific account ID to sync}
                            {--all : Sync all accounts}
                            {--force : Force sync even if recently synced}
                            {--queue : Run sync in background queue}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Facebook CPR data for monitoring';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting Facebook CPR data sync...');

        $accountId = $this->option('account');
        $syncAll = $this->option('all');
        $force = $this->option('force');
        $useQueue = $this->option('queue');

        if (!$accountId && !$syncAll) {
            $this->error('Please specify either --account=ID or --all option');
            return 1;
        }

        try {
            if ($syncAll) {
                $this->syncAllAccounts($force, $useQueue);
            } else {
                $this->syncSingleAccount($accountId, $force, $useQueue);
            }

            $this->info('Facebook CPR data sync completed successfully!');
            return 0;

        } catch (\Exception $e) {
            $this->error('Sync failed: ' . $e->getMessage());
            Log::error('CPR sync command failed', [
                'account_id' => $accountId,
                'sync_all' => $syncAll,
                'error' => $e->getMessage()
            ]);
            return 1;
        }
    }

    /**
     * Sync all accounts
     */
    private function syncAllAccounts(bool $force, bool $useQueue): void
    {
        // Get all unique account IDs from existing data
        $accountIds = OptimizedMonitoringCpr::select('account_id')
            ->distinct()
            ->pluck('account_id')
            ->toArray();

        if (empty($accountIds)) {
            $this->warn('No accounts found to sync');
            return;
        }

        $this->info("Found " . count($accountIds) . " accounts to sync");

        $bar = $this->output->createProgressBar(count($accountIds));
        $bar->start();

        foreach ($accountIds as $accountId) {
            if ($this->shouldSync($accountId, $force)) {
                if ($useQueue) {
                    SyncFacebookCprData::dispatchScheduled($accountId);
                    $this->line(" Queued sync for account: {$accountId}");
                } else {
                    $this->syncAccountDirectly($accountId);
                }
            } else {
                $this->line(" Skipped account {$accountId} (recently synced)");
            }
            
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
    }

    /**
     * Sync single account
     */
    private function syncSingleAccount(string $accountId, bool $force, bool $useQueue): void
    {
        // Ensure account ID has proper format
        $formattedAccountId = strpos($accountId, 'act_') === 0 ? $accountId : 'act_' . $accountId;

        if (!$this->shouldSync($formattedAccountId, $force)) {
            $this->warn("Account {$formattedAccountId} was recently synced. Use --force to override.");
            return;
        }

        $this->info("Syncing account: {$formattedAccountId}");

        if ($useQueue) {
            SyncFacebookCprData::dispatch($formattedAccountId);
            $this->info("Sync job queued for account: {$formattedAccountId}");
        } else {
            $this->syncAccountDirectly($formattedAccountId);
        }
    }

    /**
     * Check if account should be synced
     */
    private function shouldSync(string $accountId, bool $force): bool
    {
        if ($force) {
            return true;
        }

        $lastSync = OptimizedMonitoringCpr::forAccount($accountId)
            ->latest('last_synced_at')
            ->first();

        return !$lastSync || $lastSync->last_synced_at->diffInHours(now()) >= 1;
    }

    /**
     * Sync account directly (not queued)
     */
    private function syncAccountDirectly(string $accountId): void
    {
        try {
            $service = app(\App\Services\MonitoringCpr\OptimizedDataMonitoringCprService::class);
            $result = $service->syncFromFacebookApi($accountId);

            if ($result['success']) {
                $this->info("✓ Successfully synced account: {$accountId}");
                if (isset($result['stats'])) {
                    $this->line("  - Campaigns processed: " . ($result['stats']['total_campaigns'] ?? 0));
                    $this->line("  - Records updated: " . ($result['stats']['updated_records'] ?? 0));
                }
            } else {
                $this->warn("⚠ Sync completed with warnings for account: {$accountId}");
                $this->line("  Message: " . ($result['message'] ?? 'Unknown issue'));
            }

        } catch (\Exception $e) {
            $this->error("✗ Failed to sync account: {$accountId}");
            $this->line("  Error: " . $e->getMessage());
            throw $e;
        }
    }
}
