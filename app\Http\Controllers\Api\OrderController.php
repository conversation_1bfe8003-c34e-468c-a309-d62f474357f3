<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\Order; // Ensure the Order model exists in this namespace
use App\Models\User; // Ensure the User model exists in this namespace
use App\Models\Product; // Ensure the User model exists in this namespace
use App\Models\ProductResponsibleUser; // Ensure the ProductResponsibleUser model exists in this namespace
use Carbon\Carbon; // Import Carbon for date handling
use App\Notifications\NewOrderNotification;
use App\Events\OrderCreated; // Import the OrderCreated event
use Illuminate\Support\Facades\Response; // Import Response for JSON responses
use Illuminate\Support\Facades\Log; // Import Log for logging
use App\Helpers\WooWa;
use Illuminate\Support\Facades\Auth; // Import Auth for user authentication

class OrderController extends Controller
{
    // Get all orders
    public function index()
    {
        $orders = Order::all();
        return Response::json($orders);
    }

    // Get a single order
    public function show($id)
    {
        $order = Order::find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        return response()->json($order);
    }

    // Create a new order
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'product' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:255',
            'address' => 'required|string',
            'province' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'subdistrict' => 'required|string|max:255',
            'zip' => 'required|string|max:10',
            'payment_method' => 'required|string|max:255',
            'product_price' => 'required|numeric',
            'product_code' => 'required|string|max:255',
            'quantity' => 'required|integer',
            'courier' => 'required|string|max:255',
            'shipping_cost' => 'required|numeric',
            'gross_revenue' => 'required|numeric',
            'net_revenue' => 'required|numeric',
            'weight' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $validatedData = $validator->validated();

        try {
            // Start a database transaction
            \DB::beginTransaction();

            $datePrefix = Carbon::now()->format('ymd'); // Format: yyMMdd (e.g., 250513 for 2025-05-13)

            // Lock the table to prevent race conditions
            $lastOrder = Order::where('order_id', 'like', "$datePrefix%")
                ->orderBy('order_id', 'desc')
                ->lockForUpdate()
                ->first();

            if ($lastOrder) {
                $lastOrderNumber = (int) substr($lastOrder->order_id, -4); // Extract the last 4 digits
                $newOrderNumber = $lastOrderNumber + 1;
            } else {
                $newOrderNumber = 1;
            }

            $validatedData['order_id'] = sprintf('%s%04d', $datePrefix, $newOrderNumber); // Combine date prefix and zero-padded number

            $order = new Order;
            $order->order_id = $validatedData['order_id'];
            $order->product = $validatedData['product'];
            $order->name = $validatedData['name'];
            $order->phone = $validatedData['phone'];
            $order->address = $validatedData['address'];
            $order->province = $validatedData['province'];
            $order->city = $validatedData['city'];
            $order->subdistrict = $validatedData['subdistrict'];
            $order->zip = $validatedData['zip'];
            $order->status = 'pending';
            $order->payment_status = 'unpaid';
            $order->payment_method = $validatedData['payment_method'];
            $order->product_price = $validatedData['product_price'];
            $order->product_code = $validatedData['product_code'];
            $order->cogs = 0;
            $order->quantity = $validatedData['quantity'];
            $order->bump = 0;
            $order->bump_price = 0;
            $order->notes = $request->notes;
            $order->order_type = 'form';
            $order->courier = $validatedData['courier'];
            $order->shipping_cost = $validatedData['shipping_cost'];
            $order->cod_cost = 0;
            $order->shipping_markup = 0;
            $order->other_cost = 0;
            $order->shipping_method = 'shipped';
            $order->gross_revenue = 0;
            $order->net_revenue = 0;
            $order->variation = $request->variation;
            $order->weight = $validatedData['weight'];
            $order->handled_by = $this->assignCustomerService($validatedData['product_code']);
            $order->original_shipping_cost = $validatedData['shipping_cost'];
            $order->ip_address = $request->ip();
            $order->save();
            // $order->true;

            // Commit the transaction
            \DB::commit();

            // return response()->json($order, 201);
            $user = User::where('name', $order->handled_by)->first(); // atau sesuai user kamu
            $user->notify(new NewOrderNotification());
            // $data = (object)[
            //     'title' => $order->name . ' Memesan ' . $order->product,
            //     'created_at' => Carbon::now()->format('Y-m-d H:i:s'),
            //     'order_id' => $order->order_id,
            // ];
            // $eventResult = event(new OrderCreated($data));

            $phone_no = preg_replace('/^0/', '+62', preg_replace('/[^0-9]/', '', $order->phone));
            $cs_name = $order->handled_by ?? 'Customer Service';
            $message = "Selamat Datang di Toko kami kak {$order->name} ☺️\n\n" .
                "Perkenalkan saya {$cs_name} yang akan membantu kakak.\n" .
                "Saya sudah terima pesanan anda dengan rincian sebagai berikut,\n" .
                "Order ID: {$order->order_id}\n" .
                "Produk: {$order->product_name}, {$order->variation},\n" .
                "              {$order->bump_name}\n" .
                "Harga: {$order->product_price}, {$order->bump_price}\n" .
                "Kode Unik: {$order->unique_code}\n" .
                "Ongkir: {$order->shipping_cost}\n" .
                "Total: {$order->total_price}\n\n" .
                "Dikirim ke:\n" .
                "Nama: {$order->name}\n" .
                "No HP: {$order->phone}\n" .
                "Alamat: {$order->address}\n" .
                "Kota: {$order->city}\n" .
                "Kecamatan: {$order->district}\n\n" .
                "Apakah orderan tersebut sudah benar kak?\n\n" .
                "Jika sudah dipastikan alamat benar, silahkan transfer senilai {$order->total_price}, ke salah satu rekening dibawah ini ya kak:\n" .
                "BCA 6140767997 an ATHIYAH LAILATUN NABILAH\n" .
                "BNI 1921260852 an ATHIYAH LAILATUN NABILAH\n" .
                "BRI 371601000010568  an ATHIYAH LAILATUN NABILAH\n\n" .
                "Jika ingin mengubah metode pembayaran menjadi COD, silahkan balas ini dengan menulis YA, SAYA MAU COD\n" .
                "Saya akan siap membantu memproses transaksinya secepatnya.\n\n" .
                "Terima Kasih.\n" .
                "{$cs_name}";

            // Send message using WooWa with CS name for configuration
            WooWa::sendMessage($phone_no, $message, 'send_message', $cs_name);

            return response()->json([
                'success' => true,
                'message' => 'Order created successfully',
                'order' => $order
            ], 201);
        } catch (\Exception $e) {
            // Rollback the transaction in case of an error
            \DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to create order',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Update an existing order
    public function update(Request $request, $id)
    {
        $order = Order::find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $validatedData = $request->validate([
            'customer_name' => 'sometimes|required|string|max:255',
            'product' => 'sometimes|required|string|max:255',
            'quantity' => 'sometimes|required|integer',
            'price' => 'sometimes|required|numeric',
        ]);

        $order->update($validatedData);

        return response()->json($order);
    }

    // Delete an order
    public function destroy($id)
    {
        $order = Order::find($id);

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        $order->delete();

        return response()->json(['message' => 'Order deleted successfully']);
    }

    /**
     * Assign customer service agent based on distribution type for a given product.
     * Distribution types: 'equal', 'percentage', 'fixed'
     */
    protected function assignCustomerService($productCode = null)
    {
        // Get product_code from request if not provided
        if (!$productCode) {
            $productCode = request('product_code') ?? request('product');
        }

        // Cari product_id dari product_code
        $product = Product::where('product_code', $productCode)->first();
        if (!$product) {
            return null;
        }
        $productId = $product->id;

        // Fetch responsible users for the product
        $responsibles = ProductResponsibleUser::with('user')->where('product_id', $productId)->get();

        if ($responsibles->isEmpty()) {
            // Fallback: no responsible user, return null or default
            return null;
        }

        // Assume all have the same distribution_type for this product
        $distributionType = $responsibles->first()->distribution_type ?? 'equal';

        // Prepare rotator array
        $rotator = [];
        foreach ($responsibles as $r) {
            $rotator[] = [
                'name' => $r->user->name ?? 'Unknown',
                'distribution_value' => $r->distribution_value ?? 0,
            ];
        }

        // Get total orders today for this product
        $today = \Carbon\Carbon::today();
        $totalOrdersToday = Order::where('product_code', $productCode)
            ->whereDate('created_at', $today)
            ->count();

        switch ($distributionType) {
            case 'percentage':
                // Build weighted array based on distribution_value as percentage
                $weighted = [];
                foreach ($rotator as $agent) {
                    for ($i = 0; $i < $agent['distribution_value']; $i++) {
                        $weighted[] = $agent['name'];
                    }
                }
                return $weighted ? $weighted[array_rand($weighted)] : $rotator[0]['name'];

            case 'fixed':
                $cycleLength = array_sum(array_column($rotator, 'distribution_value'));
                $orderIndex = $cycleLength > 0 ? $totalOrdersToday % $cycleLength : 0;
                $cumulative = 0;
                foreach ($rotator as $agent) {
                    $cumulative += $agent['distribution_value'];
                    if ($orderIndex < $cumulative) {
                        return $agent['name'];
                    }
                }
                // Fallback
                return $rotator[0]['name'];

            case 'equal':
            default:
                $agentCount = count($rotator);
                $index = $agentCount > 0 ? $totalOrdersToday % $agentCount : 0;
                return $rotator[$index]['name'];
        }
    }
}
