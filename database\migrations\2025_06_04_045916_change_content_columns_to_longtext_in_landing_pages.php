<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Change content columns from TEXT to LONGTEXT to support HTML formatting
        DB::statement('ALTER TABLE landing_pages MODIFY content_description LONGTEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section2_content LONGTEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section3_content LONGTEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section4_content LONGTEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section5_content LONGTEXT');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert back to TEXT columns
        DB::statement('ALTER TABLE landing_pages MODIFY content_description TEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section2_content TEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section3_content TEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section4_content TEXT');
        DB::statement('ALTER TABLE landing_pages MODIFY section5_content TEXT');
    }
};
