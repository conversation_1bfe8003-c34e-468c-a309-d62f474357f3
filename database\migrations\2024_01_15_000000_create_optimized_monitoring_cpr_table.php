<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('optimized_monitoring_cpr', function (Blueprint $table) {
            $table->id();

            // Essential campaign identifiers
            $table->string('campaign_id')->index(); // Facebook campaign ID
            $table->string('campaign_name');
            $table->string('account_id')->index(); // Facebook account ID

            // Campaign status and timing
            $table->enum('status', ['ACTIVE', 'PAUSED', 'DELETED', 'ARCHIVED'])->default('ACTIVE');
            $table->date('campaign_start_date')->nullable();

            // Performance metrics (in original currency - usually USD)
            $table->decimal('spend', 15, 4)->default(0); // Amount spent
            $table->integer('results')->default(0); // Number of purchases/conversions
            $table->decimal('cpr', 15, 4)->default(0); // Cost per result

            // Data freshness tracking
            $table->date('data_date'); // Which date this data represents
            $table->timestamp('last_synced_at'); // When this was last updated from FB API

            // Performance optimization
            $table->index(['account_id', 'data_date']);
            $table->index(['campaign_id', 'data_date']);
            $table->unique(['campaign_id', 'data_date']); // Prevent duplicates

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('optimized_monitoring_cpr');
    }
};
