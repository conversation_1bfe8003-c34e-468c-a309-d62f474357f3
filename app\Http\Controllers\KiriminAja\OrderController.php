<?php

namespace App\Http\Controllers\KiriminAja;

use App\Http\Controllers\Controller;
use App\Services\KiriminAja\NotifyKiriminAjaService;
use App\Services\KiriminAja\Order\InquiryOrderKiriminAjaService;
use App\Services\KiriminAja\Order\KiriminAjaOrderParamsService;
use App\Services\KiriminAja\Order\KiriminAjaOrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    public function __construct(
        protected KiriminAjaOrderService $kiriminAjaOrderService,
        protected KiriminAjaOrderParamsService $kiriminAjaOrderParamsService,
        protected InquiryOrderKiriminAjaService $inquiryOrderKiriminAjaService,
        protected NotifyKiriminAjaService $notifyKiriminAjaService
    ) {
    }

    public function createOrder(Request $request)
    {
        try {
            // Buat param Order kirimaja
            $params = $this->kiriminAjaOrderParamsService->buildShippingParams($request);
            $order = $this->kiriminAjaOrderService->createOrder($params);
            if ($order['status']) {
                // simpan inquiry order dan response auto rollback jika gagal
                DB::transaction(function () use ($order, $params) {
                    // simpan inquiry order
                    $inquiryOrder = $this->inquiryOrderKiriminAjaService->create($params);
                    // simpan response
                    $this->notifyKiriminAjaService->create([
                        'id_inquiry_kirimin_aja' => $inquiryOrder->id,
                        'response' => json_encode($order)
                    ]);
                });

                return response()->json($order, 200);
            }

            return response()->json($order, 500);
        } catch (\Exception $e) {
            return response()->json($e->getMessage(), 500);
        }
    }
}
