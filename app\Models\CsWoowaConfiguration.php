<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CsWoowaConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'woowa_phone_num',
        'woowa_base_url',
        'woowa_api_key',
        'is_active',
        'last_tested_at',
        'last_test_result',
    ];

    protected $casts = [
        'woowa_api_key' => 'encrypted',
        'is_active' => 'boolean',
        'last_tested_at' => 'datetime',
        'last_test_result' => 'array',
    ];

    /**
     * Relationship dengan User (CS)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope untuk konfigurasi aktif
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk CS yang sedang login
     */
    public function scopeForCurrentUser($query)
    {
        return $query->where('user_id', auth()->id());
    }

    /**
     * Get konfigurasi untuk CS tertentu
     */
    public static function getConfigForUser($userId)
    {
        return static::where('user_id', $userId)->first() ?? new static([
            'user_id' => $userId,
            'woowa_base_url' => 'https://notifapi.com',
            'is_active' => true,
        ]);
    }

    /**
     * Get konfigurasi untuk CS yang sedang login
     */
    public static function getConfigForCurrentUser()
    {
        return static::getConfigForUser(auth()->id());
    }

    /**
     * Update hasil test connection
     */
    public function updateTestResult($result, $success = false)
    {
        $this->update([
            'last_tested_at' => now(),
            'last_test_result' => [
                'success' => $success,
                'message' => $result['message'] ?? 'No message',
                'tested_at' => now()->toISOString(),
                'response_data' => $result['data'] ?? null,
            ]
        ]);
    }

    /**
     * Check apakah konfigurasi sudah lengkap
     */
    public function isComplete(): bool
    {
        return !empty($this->woowa_phone_num) &&
            !empty($this->woowa_base_url) &&
            !empty($this->woowa_api_key);
    }

    /**
     * Check apakah test terakhir berhasil
     */
    public function isLastTestSuccessful(): bool
    {
        return $this->last_test_result &&
            isset($this->last_test_result['success']) &&
            $this->last_test_result['success'] === true;
    }

    /**
     * Get formatted phone number untuk display
     */
    public function getFormattedPhoneAttribute(): string
    {
        if (empty($this->woowa_phone_num)) {
            return '-';
        }

        $phone = $this->woowa_phone_num;
        if (strlen($phone) >= 10) {
            return substr($phone, 0, 3) . 'xxxx' . substr($phone, -3);
        }

        return $phone;
    }

    /**
     * Get status badge untuk tampilan
     */
    public function getStatusBadgeAttribute(): string
    {
        if (!$this->isComplete()) {
            return '<span class="badge bg-warning">Belum Lengkap</span>';
        }

        if (!$this->is_active) {
            return '<span class="badge bg-secondary">Tidak Aktif</span>';
        }

        if ($this->last_tested_at === null) {
            return '<span class="badge bg-info">Belum Ditest</span>';
        }

        if ($this->isLastTestSuccessful()) {
            return '<span class="badge bg-success">Koneksi OK</span>';
        }

        return '<span class="badge bg-danger">Koneksi Bermasalah</span>';
    }
}
