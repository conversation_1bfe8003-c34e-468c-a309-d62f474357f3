<?php

namespace App\Http\Requests\DataMaster;

use Illuminate\Foundation\Http\FormRequest;

class BiayaIklanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tanggal' => 'required|date',
            'team_ads' => 'required|string',
            'debit' => 'required|numeric',
            'akun_iklan' => 'required|string',
            'tertagih_real_jago' => 'required|string',
            'tertagih_fb' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'debit.required' => 'Debit wajib diisi',
            'debit.numeric' => 'Debit harus berupa angka',
            'akun_iklan.required' => 'Akun Iklan wajib diisi',
            'akun_iklan.string' => 'Akun Iklan harus berupa string',
            'tertagih_real_jago.required' => 'Tertagih Real Jago wajib diisi',
            'tertagih_real_jago.string' => 'Akun Iklan harus berupa string',
            'tertagih_fb.required' => 'Tertagih FB wajib diisi',
            'tertagih_fb.string' => 'Akun Iklan harus berupa string',
            'team_ads.required' => 'Tim Ads wajib diisi',
            'team_ads.string' => 'Tim Ads harus berupa string',
        ];
    }
}
