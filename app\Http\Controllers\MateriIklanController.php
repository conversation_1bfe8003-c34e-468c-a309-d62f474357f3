<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMateriIklanRequest;
use App\Services\MateriIklan\DataMateriIklanService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Helpers\TimeoutHelper;
use App\Enums\MateriIklan\MateriIklanStatus;

class MateriIklanController extends Controller
{
    public function __construct(protected DataMateriIklanService $dataMateriIklanService){}
    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('materi-iklan.index', [
            'title'   => 'Materi Iklan',
        ]);
    }    

    public function data()
    {
        if (request()->has('date')) {
            $dateRange = request()->get('date');
        }
        $data = $this->dataMateriIklanService->getDataIdUnique($dateRange);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('action', function ($data) {
                return '
                    <span class="text-info cursor-pointer btn-edit" data-id="' . $data->id . '">
                        <i class="bx bx-message-square-edit icon-md"></i>
                    </span>
                    <span class="text-danger cursor-pointer btn-delete" data-id="' . $data->id . '">
                        <i class="bx bx-trash icon-md"></i>
                    </span>
                ';
            })
            ->addColumn('status', function ($data) {
                $badgeClass = ($data->status)->badgeClass();
                    return '
                        <div class="badge rounded-pill ' . $badgeClass . ' p-2 text-uppercase px-3 cursor-pointer btn-status" data-id="' . $data->id . '">
                            <i class="bx bxs-circle align-middle me-1"></i>
                            ' . $data->status->value . '
                        </div>
                    ';
            })            
            ->rawColumns(['action', 'status'])
            ->make(true);        
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('materi-iklan.modal', [
            'title' => 'Data Materi Iklan',            
            'isEditing' => false
        ]);
    }

    public function store(DataMateriIklanRequest $request)
    {        
        $this->dataMateriIklanService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    public function edit(string $id)
    {
        $data = $this->dataMateriIklanService->show($id);
        return view('materi-iklan.modal', [
            'title' => 'Data Materi Iklan',
            'data' => $data,            
            'isEditing' => true
        ]);
    }

    public function update(DataMateriIklanRequest $request, string $id)
    {
        $this->dataMateriIklanService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {        
        $this->dataMateriIklanService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }

    public function updateStatus(string $id)
    {
        // return $id;
        try {
            $this->dataMateriIklanService->updateStatus($id);
            return response()->json([
                'status' => 'success',
                'message' => 'Status berhasil diubah'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Status gagal diubah: ' . $e->getMessage()
            ]);
        }
    }
}
