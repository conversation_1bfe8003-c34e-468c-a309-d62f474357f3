<?php

namespace App\Http\Controllers\KiriminAja;

use App\Http\Controllers\Controller;
use App\Services\KiriminAja\Area\KiriminAjaAreaService;
use Illuminate\Http\Request;

class WilayahController extends Controller
{
    public function __construct(protected KiriminAjaAreaService $kiriminAjaAreaService) {}

    public function getProvinces()
    {
        $provinces = $this->kiriminAjaAreaService->getProvinces();
        return response()->json($provinces);
    }

    public function getCities(string $id)
    {
        $cities = $this->kiriminAjaAreaService->getCities($id);
        return response()->json($cities);
    }

    public function getDistricts(string $id)
    {
        $districts = $this->kiriminAjaAreaService->getDistricts($id);
        return response()->json($districts);
    }

    public function getSubDistricts(string $id)
    {
        $subDistricts = $this->kiriminAjaAreaService->getSubDistricts($id);
        return response()->json($subDistricts);
    }
}
