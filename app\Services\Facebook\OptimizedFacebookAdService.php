<?php

namespace App\Services\Facebook;

use Log;
use FacebookAds\Api;
use App\Models\ApiKey;
use FacebookAds\Object\AdAccount;
use FacebookAds\Object\Fields\CampaignFields;
use FacebookAds\Object\Fields\AdSetFields;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class OptimizedFacebookAdService
{
    protected $appId;
    protected $appSecret;
    protected $accessToken;

    public function __construct()
    {
        try {
            $this->loadConfigFromDatabase();

            if ($this->isConfigured()) {
                // Initialize Facebook SDK
                \FacebookAds\Api::init(
                    $this->appId,
                    $this->appSecret,
                    $this->accessToken
                );
            }
        } catch (\Exception $e) {
            Log::warning('Failed to initialize Facebook SDK: ' . $e->getMessage());
        }
    }

    /**
     * Load Facebook configuration from database
     */
    protected function loadConfigFromDatabase()
    {
        try {
            $config = Cache::remember('facebook_config_optimized', 3600, function () {
                return ApiKey::first(); // Use first() since it's singleton pattern
            });

            if (
                $config &&
                !empty($config->facebook_app_id) &&
                !empty($config->facebook_app_secret) &&
                !empty($config->facebook_access_token)
            ) {
                $this->appId = $config->facebook_app_id;
                $this->appSecret = $config->facebook_app_secret;
                $this->accessToken = $config->facebook_access_token;
            } else {
                Log::warning('Facebook configuration is incomplete or missing');
            }
        } catch (\Exception $e) {
            Log::warning('Failed to load Facebook configuration: ' . $e->getMessage());
        }
    }

    /**
     * Check if Facebook is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->appId) && !empty($this->appSecret) && !empty($this->accessToken);
    }

    /**
     * Get optimized campaign data for CPR monitoring
     */
    public function getOptimizedCampaignData(string $accountId, array $dateRange = null): array
    {
        if (!$this->isConfigured()) {
            throw new \Exception('Facebook API is not configured');
        }

        try {
            // Create cache key based on account and date range
            $cacheKey = $this->createCacheKey($accountId, $dateRange);

            // Try to get from cache first (5 minutes cache)
            return Cache::remember($cacheKey, 300, function () use ($accountId, $dateRange) {
                return $this->fetchCampaignDataFromApi($accountId, $dateRange);
            });
        } catch (\Exception $e) {
            Log::error('Failed to get campaign data', [
                'account_id' => $accountId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception('Failed to fetch campaign data: ' . $e->getMessage());
        }
    }

    /**
     * Fetch campaign data from Facebook API with optimization
     */
    private function fetchCampaignDataFromApi(string $accountId, array $dateRange = null): array
    {
        try {
            // Check if account ID already has 'act_' prefix
            $formattedAccountId = strpos($accountId, 'act_') === 0 ? $accountId : 'act_' . $accountId;
            $account = new AdAccount($formattedAccountId);

            // Get campaigns with basic fields
            $fields = [
                CampaignFields::ID,
                CampaignFields::NAME,
                CampaignFields::STATUS,
                CampaignFields::CREATED_TIME,
            ];

            $params = [
                'effective_status' => ['ACTIVE', 'PAUSED', 'ARCHIVED'],
                'limit' => 1000
            ];

            $campaigns = $account->getCampaigns($fields, $params);

            if (!$campaigns) {
                return [];
            }

            $processedData = [];
            foreach ($campaigns as $campaign) {
                try {
                    // Get insights for each campaign
                    $insightFields = ['spend', 'actions'];
                    $insightParams = [
                        'time_range' => $dateRange ?? [
                            'since' => date('Y-m-01'), // First day of current month
                            'until' => date('Y-m-t')   // Last day of current month
                        ],
                        'level' => 'campaign'
                    ];

                    $insights = $campaign->getInsights($insightFields, $insightParams);
                    $insightData = $insights->getResponse()->getContent();

                    $spend = 0;
                    $purchases = 0;

                    if (isset($insightData['data']) && !empty($insightData['data'])) {
                        $latestInsight = $insightData['data'][0];
                        $spend = isset($latestInsight['spend']) ? floatval($latestInsight['spend']) : 0;
                        $purchases = isset($latestInsight['actions']) ? $this->extractPurchaseData($latestInsight['actions']) : 0;
                    }

                    $processedData[] = [
                        'id_kampanye' => $campaign->{CampaignFields::ID},
                        'nama_kampanye' => $campaign->{CampaignFields::NAME},
                        'status' => $campaign->{CampaignFields::STATUS},
                        'tanggal_mulai' => $campaign->{CampaignFields::CREATED_TIME},
                        'biaya_dibelanjakan' => $spend,
                        'hasil' => $purchases,
                        'cpr' => $purchases > 0 ? $spend / $purchases : 0
                    ];

                } catch (\Exception $campaignError) {
                    Log::warning('Error processing campaign insights', [
                        'campaign_id' => $campaign->{CampaignFields::ID} ?? 'unknown',
                        'error' => $campaignError->getMessage()
                    ]);
                    continue;
                }
            }

            // Sort by spend descending
            usort($processedData, function ($a, $b) {
                return $b['biaya_dibelanjakan'] <=> $a['biaya_dibelanjakan'];
            });

            return $processedData;

        } catch (\Exception $e) {
            Log::error('Facebook API Error', [
                'account_id' => $accountId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception('Failed to fetch Facebook Ads data: ' . $e->getMessage());
        }
    }

    /**
     * Extract purchase data from actions array
     */
    private function extractPurchaseData(array $actions): int
    {
        $purchases = 0;

        foreach ($actions as $action) {
            if (!is_array($action) || !isset($action['action_type'], $action['value'])) {
                continue;
            }

            if (
                in_array($action['action_type'], [
                    'web_in_store_purchase',
                    'purchase',
                    'onsite_conversion.purchase'
                ])
            ) {
                $purchases += intval($action['value']);
            }
        }

        return $purchases;
    }

    /**
     * Create cache key for API data
     */
    private function createCacheKey(string $accountId, array $dateRange = null): string
    {
        $dateKey = $dateRange ? implode('_', $dateRange) : 'last_30d';
        return "fb_cpr_data_{$accountId}_{$dateKey}";
    }

    /**
     * Clear cache for specific account
     */
    public function clearCacheForAccount(string $accountId): void
    {
        try {
            // Clear specific cache keys for this account
            $cacheKeys = [
                "fb_cpr_data_{$accountId}_last_30d",
                "fb_account_info_{$accountId}",
                "facebook_config_optimized"
            ];

            // Generate possible date range combinations for the account
            $today = now();
            for ($i = 0; $i < 30; $i++) {
                $date = $today->copy()->subDays($i)->format('Y-m-d');
                $cacheKeys[] = "fb_cpr_data_{$accountId}_{$date}";
            }

            // Clear each cache key individually (works with all cache drivers)
            foreach ($cacheKeys as $key) {
                Cache::forget($key);
            }

            Log::debug('Cleared Facebook cache for account', [
                'account_id' => $accountId,
                'cache_keys_cleared' => count($cacheKeys)
            ]);

        } catch (\Exception $e) {
            Log::warning('Failed to clear some Facebook cache entries', [
                'account_id' => $accountId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get account information (cached)
     */
    public function getAccountInfo(string $accountId): array
    {
        return Cache::remember("fb_account_info_{$accountId}", 3600, function () use ($accountId) {
            try {
                $account = new AdAccount($accountId);
                $accountData = $account->read(['name', 'currency', 'timezone_name']);

                return [
                    'id' => $accountId,
                    'name' => $accountData->name ?? 'Unknown Account',
                    'currency' => $accountData->currency ?? 'USD',
                    'timezone' => $accountData->timezone_name ?? 'UTC'
                ];
            } catch (\Exception $e) {
                Log::warning("Failed to fetch account info for {$accountId}: " . $e->getMessage());
                return [
                    'id' => $accountId,
                    'name' => 'Account ' . $accountId,
                    'currency' => 'USD',
                    'timezone' => 'UTC'
                ];
            }
        });
    }

    /**
     * Test connection with lightweight call
     */
    public function testConnection(): array
    {
        try {
            if (!$this->isConfigured()) {
                return [
                    'success' => false,
                    'message' => 'Facebook API is not configured'
                ];
            }

            // Test with user 'me' endpoint which is lightweight
            $user = new \FacebookAds\Object\User('me');
            $user->read(['id', 'name']);

            return [
                'success' => true,
                'message' => 'Connection successful',
                'data' => [
                    'user_id' => $user->id ?? 'Unknown',
                    'user_name' => $user->name ?? 'Unknown'
                ]
            ];

        } catch (\FacebookAds\Exception\AuthorizationException $e) {
            return [
                'success' => false,
                'message' => 'Authorization failed: Invalid access token or insufficient permissions'
            ];
        } catch (\FacebookAds\Exception\AuthenticationException $e) {
            return [
                'success' => false,
                'message' => 'Authentication failed: Invalid App ID or App Secret'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Refresh configuration cache
     */
    public function refreshConfig(): void
    {
        Cache::forget('facebook_config_optimized');
        $this->loadConfigFromDatabase();

        if ($this->isConfigured()) {
            Api::init($this->appId, $this->appSecret, $this->accessToken, false);
        }
    }
}
