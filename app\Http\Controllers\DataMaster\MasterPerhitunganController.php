<?php

namespace App\Http\Controllers\DataMaster;

use App\Http\Controllers\Controller;
use App\Http\Requests\DataMaster\PerhitunganRequest;
use App\Services\Perhitungan\PerhitunganService;
use Carbon\Carbon;
use Yajra\DataTables\Facades\DataTables;

class MasterPerhitunganController extends Controller
{
    public function __construct(protected PerhitunganService $PerhitunganService){}

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('data-master.perhitungan.index', [
            'title' => 'Master Perhitungan'
        ]);
    }

    public function data()
    {
        $perhitungan = $this->PerhitunganService->grid();
        return DataTables::of($perhitungan)
            ->addIndexColumn()
            ->addColumn('created_at', function ($hitung) {
                return Carbon::parse($hitung->created_at)->translatedFormat('d F Y, H:i');
            })
            ->addColumn('updated_at', function ($hitung) {
                return Carbon::parse($hitung->updated_at)->translatedFormat('d F Y, H:i');
            })
            ->addColumn('action', function ($hitung) {
                return '
                    <span class="text-info cursor-pointer btn-edit" data-id="' . $hitung->id . '">
                        <i class="bx bx-message-square-edit icon-md"></i>
                    </span>
                    <span class="text-danger cursor-pointer btn-delete" data-id="' . $hitung->id . '">
                        <i class="bx bx-trash icon-md"></i>
                    </span>
                ';
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.perhitungan.modal', [
            'title' => 'Master Perhitungan',
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PerhitunganRequest $request)
    {
        $this->PerhitunganService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = $this->PerhitunganService->find($id);
        return view('data-master.perhitungan.modal', [
            'title' => 'Perhitungan',
            'data' => $data,
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PerhitunganRequest $request, string $id)
    {
        $this->PerhitunganService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->PerhitunganService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }
}
