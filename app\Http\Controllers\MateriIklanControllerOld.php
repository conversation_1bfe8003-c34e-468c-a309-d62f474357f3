<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMateriIklanRequest;
use App\Services\MateriIklan\DataMateriIklanService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use App\Services\Facebook\FacebookAdService;
use App\Helpers\TimeoutHelper;

class MateriIklanControllerOld extends Controller
{
    public function __construct(
        protected DataMateriIklanService $dataMateriIklanService,
        protected FacebookAdService $facebookAdService
    ) {
        $this->facebookAdService = $facebookAdService;
    }

    /**
     * <PERSON><PERSON>lan pagawai.
     */
    public function index()
    {
        $idAkunString = auth()->user()->fbads_id;
        $namaAkunString = auth()->user()->ads_code;

        $idAkunArray = array_map('trim', explode(',', $idAkunString));
        $namaAkunArray = array_map('trim', explode(',', $namaAkunString));
        $akun_ads = [];

        foreach ($idAkunArray as $key => $id) {
            $akun_ads[] = [
                'id' => $id,
                'nama' => $namaAkunArray[$key] ?? null
            ];
        }

        return view('materi-iklan.index', [
            'title' => 'Materi Iklan',
            'akun' => $akun_ads
        ]);
    }


    public function data()
    {
        if (request()->has('date')) {
            $dateRange = request()->get('date');
        }
        $data = $this->dataMateriIklanService->getDataIdUnique($dateRange);
        return DataTables::of($data)
            ->addIndexColumn()
            // ->addColumn('action', function ($user) {
            //     return '
            //         <span class="text-info cursor-pointer btn-edit" data-id="' . $user->id . '">
            //             <i class="bx bx-message-square-edit icon-md"></i>
            //         </span>
            //         <span class="text-danger cursor-pointer btn-delete" data-id="' . $user->id . '">
            //             <i class="bx bx-trash icon-md"></i>
            //         </span>
            //     ';
            // })
            // ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    // public function create(Request $request)
    // {
    //     $idAkun = 'act_'.$request->idAkun;
    //     $namaAkun = $request->namaAkun;

    //     $campaign = $this->facebookAdService->getDataCampaigns($idAkun);

    //     // $campaign = TimeoutHelper::runWithTimeout(function () use ($idAkun) {
    //     //     $this->facebookAdService->getDataCampaigns($idAkun);
    //     // }, 30);

    //     // return response()->json([
    //     //     'count' => count($campaign),
    //     //     'data' => $campaign
    //     // ]);

    //     // if($campaign){
    //     //     $campaign_sub = $this->facebookAdService->getDataAd($idAkun);
    //     // }

    //     // Buat array index kampanye berdasarkan id_kampanye
    //     // $indexedCampaign = [];
    //     // foreach ($campaign as $item) {
    //     //     $indexedCampaign[$item['id_kampanye']] = $item;
    //     // }

    //     // Tambahkan data kampanye ke masing-masing iklan
    //     // foreach ($campaign_sub as &$adSet) {
    //     //     $id_kampanye = $adSet['id_kampanye'];

    //     //     if (isset($indexedCampaign[$id_kampanye])) {
    //     //         $adSet['nama_kampanye'] = $indexedCampaign[$id_kampanye]['nama_kampanye'] ?? '-';
    //     //         $adSet['status'] = $indexedCampaign[$id_kampanye]['status'] ?? '-';
    //     //         $adSet['strategi_bid'] = $indexedCampaign[$id_kampanye]['strategi_bid'] ?? '-';
    //     //         $adSet['anggaran_harian'] = $indexedCampaign[$id_kampanye]['anggaran_harian'] ?? '-';
    //     //     } else {
    //     //         // Jika kampanye tidak ditemukan, beri nilai default
    //     //         $adSet['nama_kampanye'] = '-';
    //     //         $adSet['status'] = '-';
    //     //         $adSet['strategi_bid'] = '-';
    //     //         $adSet['anggaran_harian'] = '-';
    //     //     }
    //     // }
    //     // return response()->json($campaign_sub);

    //     return view('materi-iklan.modal', [
    //         'title' => 'Kampanye (Iklan)',
    //         'levelUser' => LevelUser::toArray(),
    //         'isEditing' => false,
    //         'namaAkun'      => $namaAkun,
    //         'idAkun'      => $idAkun,
    //         'campaign'  => response()->json($campaign)
    //     ]);
    // }
    public function create(Request $request)
    {
        $idAkun = 'act_' . $request->idAkun;
        $namaAkun = $request->namaAkun;

        $insightsResponse = $this->facebookAdService->getDataInsights($idAkun); // get insight iklan
        $insights = $insightsResponse->getData(true); // Get decoded data as array

        if ($insights && isset($insights['data'])) {
            // ADSET
            $adSet = $this->facebookAdService->getDataAdSet($idAkun); // get adsetFields

            $adStatusMap = [];
            $adAtribusiMap = [];

            $adWaktuMulaiMap = [];
            $adWaktuSelesaiMap = [];

            foreach ($adSet as $ad) {
                $adStatusMap[$ad['id_kampanye']] = $ad['status'] ?? null;
                $adWaktuMulaiMap[$ad['id_kampanye']] = $ad['waktu_dibuat'] ?? null;
                $adWaktuSelesaiMap[$ad['id_kampanye']] = $ad['waktu_selesai'] ?? null;

                $atribusiList = $ad['spek_atribusi'] ?? [];
                $atribusiString = [];

                foreach ($atribusiList as $atribusi) {
                    $days = $atribusi['window_days'] ?? 0;
                    $label = match ($atribusi['event_type']) {
                        'CLICK_THROUGH' => ' klik',
                        'VIEW_THROUGH' => ' tayangan',
                        'ENGAGED_VIDEO_VIEW' => ' interakhir',
                        default => 'lainnya',
                    };
                    $atribusiString[] = $days . $label;
                }
                $adAtribusiMap[$ad['id_kampanye']] = implode(',', $atribusiString) . ' (hari)';
            }

            foreach ($insights['data'] as &$insight) {
                $idKampanye = $insight['id_kampanye'] ?? null;

                $insight['status'] = $adStatusMap[$idKampanye] ?? '-';
                $insight['spek_atribusi'] = $adAtribusiMap[$idKampanye] ?? '-';

                $insight['waktu_mulai'] = $adWaktuMulaiMap[$idKampanye] ?? '-';
                $insight['waktu_selesai'] = $adWaktuSelesaiMap[$idKampanye] ?? '-';

                $data = [
                    'jangkauan' => $insight['jangkauan'] ?? 0,
                    'impresi' => $insight['impresi'] ?? 0,
                    'biaya_dibelanjakan' => $insight['biaya_dibelanjakan'] ?? 0,
                    'biaya_per_klik' => $insight['biaya_per_klik'] ?? 0,
                    'ctr' => $insight['ctr'] ?? 0,
                    'frekuensi' => $insight['frekuensi'] ?? 0,
                    'klik' => $insight['klik'] ?? 0,
                    'biaya_per_tayangan' => $insight['biaya_per_tayangan'] ?? 0,
                    'biaya_perhasil' => $insight['biaya_perhasil'] ?? 0,
                    'nama_kampanye' => $insight['nama_kampanye'] ?? '',
                    'id_kampanye' => $insight['id_kampanye'] ?? '',
                    'nama_iklan' => $insight['nama_iklan'] ?? '',
                    'id_iklan' => $insight['id_iklan'] ?? '',
                    'nama_set_iklan' => $insight['nama_set_iklan'] ?? '',
                    'id_set_iklan' => $insight['id_set_iklan'] ?? '',
                    'jenis_pembelian' => $insight['jenis_pembelian'] ?? '',
                    'tanggal_mulai' => $insight['waktu_mulai'] ?? null,
                    'tanggal_berhenti' => $insight['waktu_selesai'] ?? null,
                    'peringkat_kualitas' => $insight['peringkat_kualitas'] ?? '',
                    'conversion_rate_ranking' => $insight['conversion_rate_ranking'] ?? '',
                    'status' => $insight['status'],
                    'spek_atribusi' => $insight['spek_atribusi'],
                    'id_akun' => $request->idAkun,

                ];
                $this->dataMateriIklanService->updateOrCreate($data);
            }
            unset($insight); // Break reference

        }

        // return $insights;
        return view('materi-iklan.modal', [
            'title' => 'Materi Iklan',
            'levelUser' => LevelUser::toArray(),
            'isEditing' => false,
            'namaAkun' => $namaAkun,
            'idAkun' => $idAkun,
            'materiIklan' => $insights,
        ]);
    }
}
