@extends('templates.app')
@section('title', $title)
@section('content')
    <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
    <hr />

    <!-- User Info Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="avatar avatar-lg me-3">
                    <div class="avatar-initial bg-label-primary rounded">
                        <i class="ti ti-user ti-md"></i>
                    </div>
                </div>
                <div>
                    <h6 class="mb-0">{{ $user->name }}</h6>
                    <small class="text-muted">{{ \App\Enums\LevelUser::getLabel($user->level_user) }}</small>
                </div>
                <div class="ms-auto">
                    <div class="d-flex align-items-center">
                        <span class="me-2">Status Konfigurasi:</span>
                        <div id="configStatus">
                            @if ($config->exists && $config->isComplete())
                                @if ($config->is_active)
                                    {!! $config->status_badge !!}
                                @else
                                    <span class="badge bg-secondary">Tidak Aktif</span>
                                @endif
                            @else
                                <span class="badge bg-warning">Belum Dikonfigurasi</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form id="wooWaConfigForm" class="needs-validation" novalidate>
        @csrf
        @method('PUT')

        <!-- WooWa Integration -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="ti ti-message-2 me-2"></i>
                    WooWa Integration Configuration
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="testConnection(this)">
                    <i class="ti ti-plug"></i> Test Connection
                </button>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h6><i class="ti ti-info-circle me-2"></i>Informasi Konfigurasi WooWa</h6>
                    <p class="mb-0">
                        Halaman ini digunakan untuk mengkonfigurasi integrasi WooWa (WhatsApp Gateway) yang digunakan
                        untuk mengirim pesan otomatis kepada pelanggan. Setiap CS memiliki konfigurasi WooWa sendiri.
                        Pastikan semua field terisi dengan benar untuk memastikan pesan dapat dikirim dengan lancar.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woowa_phone_num" class="form-label">
                                Phone Number <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="woowa_phone_num" name="woowa_phone_num"
                                   value="{{ $config->woowa_phone_num ?? '' }}"
                                   placeholder="Contoh: 628123456789"
                                   required>
                            <div class="form-text">
                                Nomor WhatsApp yang terdaftar di WooWa (format: 628123456789)
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woowa_base_url" class="form-label">
                                Base URL <span class="text-danger">*</span>
                            </label>
                            <input type="url" class="form-control" id="woowa_base_url" name="woowa_base_url"
                                   value="{{ $config->woowa_base_url ?? 'https://notifapi.com' }}"
                                   placeholder="https://notifapi.com"
                                   required>
                            <div class="form-text">
                                URL endpoint WooWa API
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woowa_api_key" class="form-label">
                                API Key <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="woowa_api_key" name="woowa_api_key"
                                       value="{{ $config->woowa_api_key ?? '' }}"
                                       placeholder="Your WooWa API Key"
                                       required>
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                API Key yang diperoleh dari dashboard WooWa
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Fields -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label class="form-label">Status Konfigurasi</label>
                            <div class="form-check form-switch">
                                <input type="hidden" name="is_active" value="0">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                       value="1" {{ $config->is_active ?? true ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Aktifkan Konfigurasi
                                </label>
                            </div>
                            <div class="form-text">
                                Nonaktifkan jika konfigurasi ini tidak digunakan sementara
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Connection -->
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    <i class="ti ti-activity me-2"></i>Status Koneksi
                                </h6>
                                <div id="connectionStatus">
                                    @if ($config->last_tested_at)
                                        @if ($config->isLastTestSuccessful())
                                            <span class="badge bg-success">
                                                <i class="ti ti-check me-1"></i>Koneksi Berhasil
                                            </span>
                                            <small class="text-muted ms-2">
                                                Terakhir test: {{ $config->last_tested_at->format('d/m/Y H:i') }}
                                            </small>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="ti ti-x me-1"></i>Koneksi Bermasalah
                                            </span>
                                            <small class="text-muted ms-2">
                                                Terakhir test: {{ $config->last_tested_at->format('d/m/Y H:i') }} -
                                                {{ $config->last_test_result['message'] ?? 'Error tidak diketahui' }}
                                            </small>
                                        @endif
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="ti ti-clock me-1"></i>Belum Ditest
                                        </span>
                                        <small class="text-muted ms-2">Klik "Test Connection" untuk memeriksa koneksi</small>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Usage Instructions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                <h6 class="card-title mb-3">
                                    <i class="ti ti-help-circle me-2"></i>Petunjuk Penggunaan
                                </h6>
                                <ol class="mb-0">
                                    <li><strong>Phone Number:</strong> Masukkan nomor WhatsApp yang sudah terdaftar di akun WooWa Anda (format internasional: 628123456789)</li>
                                    <li><strong>Base URL:</strong> Gunakan URL endpoint yang diberikan oleh WooWa (biasanya https://notifapi.com)</li>
                                    <li><strong>API Key:</strong> Masukkan API key yang Anda dapatkan dari dashboard WooWa</li>
                                    <li><strong>Test Connection:</strong> Setelah mengisi semua field, klik tombol "Test Connection" untuk memastikan konfigurasi benar</li>
                                    <li><strong>Simpan:</strong> Jika test berhasil, klik "Simpan Konfigurasi" untuk menyimpan pengaturan</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-2"></i>
                        Simpan Konfigurasi
                    </button>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('script')
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('.toggle-password').click(function() {
                const input = $(this).siblings('input');
                const icon = $(this).find('i');

                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                    icon.removeClass('ti-eye').addClass('ti-eye-off');
                } else {
                    input.attr('type', 'password');
                    icon.removeClass('ti-eye-off').addClass('ti-eye');
                }
            });

            // Form submission
            $('#wooWaConfigForm').on('submit', function(e) {
                e.preventDefault();

                const form = this;
                if (!form.checkValidity()) {
                    form.classList.add('was-validated');
                    return;
                }

                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();

                submitBtn.prop('disabled', true).html('<i class="ti ti-loader me-2"></i>Menyimpan...');

                $.ajax({
                    url: "{{ route('woowa-config.update') }}",
                    method: 'PUT',
                    data: $(this).serialize(),
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: response.message,
                                showConfirmButton: false,
                                timer: 2000
                            }).then(() => {
                                location.reload(); // Reload untuk update status
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: response.message
                            });
                        }
                    },
                    error: function(xhr) {
                        let message = 'Terjadi kesalahan saat menyimpan konfigurasi';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }

                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = xhr.responseJSON.errors;
                            const errorMessages = Object.values(errors).flat().join('<br>');
                            message += '<br><br>' + errorMessages;
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            html: message
                        });
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        });

        function testConnection(button) {
            const originalText = $(button).html();
            $(button).prop('disabled', true).html('<i class="ti ti-loader me-2"></i>Testing...');

            // Get form data
            const formData = {
                woowa_phone_num: $('#woowa_phone_num').val(),
                woowa_base_url: $('#woowa_base_url').val(),
                woowa_api_key: $('#woowa_api_key').val(),
                _token: $('meta[name="csrf-token"]').attr('content')
            };

            $.ajax({
                url: "{{ route('woowa-config.test-connection') }}",
                method: 'POST',
                data: formData,
                success: function(response) {
                    const statusDiv = $('#connectionStatus');

                    if (response.success) {
                        statusDiv.html(`
                    <span class="badge bg-success">
                        <i class="ti ti-check me-1"></i>Koneksi Berhasil
                    </span>
                    <small class="text-muted ms-2">${response.message}</small>
                `);

                        Swal.fire({
                            icon: 'success',
                            title: 'Test Berhasil!',
                            text: response.message,
                            showConfirmButton: false,
                            timer: 3000
                        });
                    } else {
                        statusDiv.html(`
                    <span class="badge bg-danger">
                        <i class="ti ti-x me-1"></i>Koneksi Gagal
                    </span>
                    <small class="text-muted ms-2">${response.message}</small>
                `);

                        Swal.fire({
                            icon: 'error',
                            title: 'Test Gagal!',
                            text: response.message
                        });
                    }
                },
                error: function(xhr) {
                    const statusDiv = $('#connectionStatus');
                    let message = 'Terjadi kesalahan saat test koneksi';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }

                    statusDiv.html(`
                <span class="badge bg-danger">
                    <i class="ti ti-x me-1"></i>Error
                </span>
                <small class="text-muted ms-2">${message}</small>
            `);

                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: message
                    });
                },
                complete: function() {
                    $(button).prop('disabled', false).html(originalText);
                }
            });
        }
    </script>
@endpush
