<?php

namespace App\Http\Controllers;

use App\Services\BiayaIklan\BiayaIklanService;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;

class BiayaIklanController extends Controller
{
    public function __construct(protected BiayaIklanService $biayaIklanService)
    {
    }

    public function index()
    {
        return view('biaya-iklan.index', [
            'title' => 'Biaya Iklan',
        ]);
    }

    public function data(Request $request)
    {
        // Filter berdasarkan handled_by
        $handledBy = $request->input('handled_by');
        // Ambil data mentah dari service
        $rawData = $this->biayaIklanService->grid($handledBy);
        // Proses data menggunakan processBiayaIklanData
        $processedData = $this->biayaIklanService->processBiayaIklanData($rawData);
        // Format data menggunakan formatForView
        $formatView = $this->biayaIklanService->formatForView($processedData);
        return DataTables::of($formatView)
            ->addIndexColumn()
            ->make(true);
    }
}
