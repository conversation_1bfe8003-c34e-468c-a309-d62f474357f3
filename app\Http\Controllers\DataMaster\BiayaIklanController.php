<?php

namespace App\Http\Controllers\DataMaster;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\BiayaIklan\BiayaIklanService;
use App\Http\Requests\DataMaster\BiayaIklanRequest;

class BiayaIklanController extends Controller
{
    public function __construct(protected BiayaIklanService $biayaIklanService)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('data-master.biaya-iklan.index', [
            'title' => 'Biaya Iklan',
            'users' => User::where('level_user', 4)->orderBy('name', 'asc')->get()
        ]);
    }

    public function data(Request $request)
    {
        // Filter berdasarkan team_ads
        $team_ads = $request->input('team_ads');
        // Ambil data mentah dari service
        $rawData = $this->biayaIklanService->grid($team_ads);
        // Proses data menggunakan processBiayaIklanData
        $processedData = $this->biayaIklanService->processBiayaIklanData($rawData);
        // Format data menggunakan formatForView
        $formatView = $this->biayaIklanService->formatForView($processedData);
        return DataTables::of($formatView)
            ->addIndexColumn()
            ->addColumn('action', function ($formatView) {
                return '
                    <span class="text-info cursor-pointer btn-edit" data-id="' . $formatView['id'] . '">
                        <i class="bx bx-message-square-edit icon-md"></i>
                    </span>
                    <span class="text-danger cursor-pointer btn-delete" data-id="' . $formatView['id'] . '">
                        <i class="bx bx-trash icon-md"></i>
                    </span>
                ';
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.biaya-iklan.modal', [
            'title' => 'Biaya Iklan',
            'users' => User::where('level_user', 4)->orderBy('name', 'asc')->get(),
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BiayaIklanRequest $request)
    {   // Format data dari request
        $formatStore = $this->biayaIklanService->formatStore($request->validated());
        // Simpan data
        $this->biayaIklanService->store($formatStore);
        // Kirim response
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = $this->biayaIklanService->show($id);
        return view('data-master.biaya-iklan.modal', [
            'title' => 'Biaya Iklan',
            'users' => User::where('level_user', 4)->orderBy('name', 'asc')->get(),
            'biayaIklan' => $data,
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BiayaIklanRequest $request, string $id)
    {
        $this->biayaIklanService->update($request->validated(), $id);
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->biayaIklanService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }
}
