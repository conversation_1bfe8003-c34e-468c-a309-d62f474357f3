<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();

            // Raja Ongkir Integration
            $table->string('rajaongkir_endpoint')->nullable();
            $table->text('rajaongkir_api_key')->nullable();

            // KiriminAja Integration
            $table->enum('kiriminaja_env', ['dev', 'prod'])->default('dev');
            $table->string('kiriminaja_dev_base_url')->nullable();
            $table->text('kiriminaja_dev_api_key')->nullable();
            $table->string('kiriminaja_prod_base_url')->nullable();
            $table->text('kiriminaja_prod_api_key')->nullable();
            $table->string('kiriminaja_base_uri')->nullable();

            // Facebook Integration
            $table->text('facebook_app_id')->nullable();
            $table->text('facebook_app_secret')->nullable();
            $table->text('facebook_access_token')->nullable();

            // WooWa Integration
            $table->string('woowa_phone_num')->nullable();
            $table->string('woowa_base_url')->nullable();
            $table->text('woowa_api_key')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
