<?php

namespace Database\Seeders;

use App\Models\ApiKey;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ApiKeySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ApiKey::updateOrCreate(
            ['id' => 1], // Singleton pattern - hanya satu record
            [
                'rajaongkir_endpoint' => 'https://pro.rajaongkir.com',
                'rajaongkir_api_key' => null,
                'kiriminaja_env' => 'dev',
                'kiriminaja_dev_base_url' => 'https://api-sandbox.kiriminaja.com',
                'kiriminaja_dev_api_key' => null,
                'kiriminaja_prod_base_url' => 'https://api.kiriminaja.com',
                'kiriminaja_prod_api_key' => null,
                'kiriminaja_base_uri' => 'https://api-sandbox.kiriminaja.com',
                'facebook_app_id' => null,
                'facebook_app_secret' => null,
                'facebook_access_token' => null,
                'woowa_phone_num' => null,
                'woowa_base_url' => 'https://notifapi.com',
                'woowa_api_key' => null,
            ]
        );
    }
}
