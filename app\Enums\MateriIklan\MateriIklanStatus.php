<?php

namespace App\Enums\MateriIklan;

enum MateriIklanStatus: string
{
    case DRAFT = 'draft';
    case SIAP_DIGUNAKAN = 'siap_digunakan';
    case DIGUNAKAN = 'digunakan';

    public function badgeClass(): string
    {
        return match ($this) {
            self::DRAFT => 'text-warning bg-light-warning',
            self::SIAP_DIGUNAKAN => 'text-info bg-light-info',
            self::DIGUNAKAN => 'text-success bg-light-success',            
            default => 'text-secondary bg-light-secondary',
        };
    }

    public function toggle()
    {
        return match ($this) {
            self::DRAFT => self::SIAP_DIGUNAKAN,
            self::SIAP_DIGUNAKAN => self::DIGUNAKAN,
            self::DIGUNAKAN => self::DIGUNAKAN,            
        };
    }
}
