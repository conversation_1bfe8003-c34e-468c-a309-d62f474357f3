<?php

namespace App\Http\Controllers\Laporan;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\CashbackRequest;
use App\Services\Laporan\CashbackService;
use Yajra\DataTables\Facades\DataTables;
use App\Models\User;

class LaporanCashBackController extends Controller
{
    public function __construct(protected CashbackService $cashbackServiceService){}

    /**
     * <PERSON>pilan pagawai.
     */
    public function index()
    {
        return view('laporan.cashback.index', [
            'title' => 'Laporan Cashback',
        ]);
    }

    public function data()
    {
        $data = $this->cashbackServiceService->grid();
        return DataTables::of($data)
            ->addIndexColumn()
            // ->addColumn('action', function ($data) {
            //     return '
            //         <span class="text-info cursor-pointer btn-edit" data-id="' . $data->id . '">
            //             <i class="bx bx-message-square-edit icon-md"></i>
            //         </span>
            //         <span clzass="text-danger cursor-pointer btn-delete" data-id="' . $data->id . '">
            //             <i class="bx bx-trash icon-md"></i>
            //         </span>
            //     ';
            // })
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('laporan.cashback.add-cashback', [
            'title' => 'Cashback',
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CashbackRequest $request)
    {
        $request->bulan = date('m',strtotime($request->bulanTahun));
        $request->tahun = date('Y',strtotime($request->bulanTahun));
        $this->cashbackServiceService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = $this->cashbackServiceService->show($id);
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'pegawai' => $user,
            'levelUser' => LevelUser::toArray(),
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CashbackRequest $request, string $id)
    {
        $this->cashbackServiceService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->cashbackServiceService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }
}
