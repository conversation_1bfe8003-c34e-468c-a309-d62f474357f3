@extends('templates.app')
@section('title', $title)
@section('content')
    <h6 class="mb-0 text-uppercase">{{ $title }}</h6>
    <hr />

    <form id="apiKeyForm" class="needs-validation" novalidate>
        @csrf
        @method('PUT')

        <!-- Raja <PERSON>r Integration -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="ti ti-truck-delivery me-2"></i>
                    Raja Ongkir Integration
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="testConnection('rajaongkir', this)">
                    <i class="ti ti-plug"></i> Test Connection
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="rajaongkir_endpoint" class="form-label">Endpoint</label>
                            <input type="url" class="form-control" id="rajaongkir_endpoint" name="rajaongkir_endpoint"
                                   value="{{ $apiKey->rajaongkir_endpoint ?? 'https://pro.rajaongkir.com' }}"
                                   placeholder="https://pro.rajaongkir.com">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="rajaongkir_api_key" class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="rajaongkir_api_key" name="rajaongkir_api_key"
                                       value="{{ $apiKey->rajaongkir_api_key ?? '' }}"
                                       placeholder="Your RajaOngkir API Key">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KiriminAja Integration -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="ti ti-package me-2"></i>
                    KiriminAja Integration
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="testConnection('kiriminaja', this)">
                    <i class="ti ti-plug"></i> Test Connection
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="mb-3">
                            <label for="kiriminaja_env" class="form-label">Environment</label>
                            <select class="form-select" id="kiriminaja_env" name="kiriminaja_env" required>
                                <option value="dev" {{ ($apiKey->kiriminaja_env ?? 'dev') == 'dev' ? 'selected' : '' }}>Development</option>
                                <option value="prod" {{ ($apiKey->kiriminaja_env ?? 'dev') == 'prod' ? 'selected' : '' }}>Production</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kiriminaja_dev_base_url" class="form-label">Development Base URL</label>
                            <input type="url" class="form-control" id="kiriminaja_dev_base_url" name="kiriminaja_dev_base_url"
                                   value="{{ $apiKey->kiriminaja_dev_base_url ?? 'https://tdev.kiriminaja.com' }}"
                                   placeholder="https://tdev.kiriminaja.com">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kiriminaja_dev_api_key" class="form-label">Development API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="kiriminaja_dev_api_key" name="kiriminaja_dev_api_key"
                                       value="{{ $apiKey->kiriminaja_dev_api_key ?? '' }}"
                                       placeholder="Your Development API Key">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kiriminaja_prod_base_url" class="form-label">Production Base URL</label>
                            <input type="url" class="form-control" id="kiriminaja_prod_base_url" name="kiriminaja_prod_base_url"
                                   value="{{ $apiKey->kiriminaja_prod_base_url ?? 'https://api.kiriminaja.com' }}"
                                   placeholder="https://api.kiriminaja.com">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kiriminaja_prod_api_key" class="form-label">Production API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="kiriminaja_prod_api_key" name="kiriminaja_prod_api_key"
                                       value="{{ $apiKey->kiriminaja_prod_api_key ?? '' }}"
                                       placeholder="Your Production API Key">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Facebook Integration -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="ti ti-brand-facebook me-2"></i>
                    Facebook Integration
                </h5>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="testConnection('facebook', this)">
                    <i class="ti ti-plug"></i> Test Connection
                </button>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="facebook_app_id" class="form-label">App ID</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="facebook_app_id" name="facebook_app_id"
                                       value="{{ $apiKey->facebook_app_id ?? '' }}"
                                       placeholder="Your Facebook App ID">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="facebook_app_secret" class="form-label">App Secret</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="facebook_app_secret" name="facebook_app_secret"
                                       value="{{ $apiKey->facebook_app_secret ?? '' }}"
                                       placeholder="Your Facebook App Secret">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="facebook_access_token" class="form-label">Access Token</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="facebook_access_token" name="facebook_access_token"
                                       value="{{ $apiKey->facebook_access_token ?? '' }}"
                                       placeholder="Your Facebook Access Token">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- WooWa Integration -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="ti ti-message-2 me-2"></i>
                    WooWa Integration
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woowa_phone_num" class="form-label">Phone Number</label>
                            <input type="text" class="form-control" id="woowa_phone_num" name="woowa_phone_num"
                                   value="{{ $apiKey->woowa_phone_num ?? '' }}"
                                   placeholder="Phone Number">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woowa_base_url" class="form-label">Base URL</label>
                            <input type="url" class="form-control" id="woowa_base_url" name="woowa_base_url"
                                   value="{{ $apiKey->woowa_base_url ?? 'https://notifapi.com' }}"
                                   placeholder="https://notifapi.com">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woowa_api_key" class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="woowa_api_key" name="woowa_api_key"
                                       value="{{ $apiKey->woowa_api_key ?? '' }}"
                                       placeholder="Your WooWa API Key">
                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                    <i class="ti ti-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="refreshConfig()">
                            <i class="ti ti-refresh me-2"></i>
                            Refresh Cache
                        </button>
                        <button type="button" class="btn btn-outline-info me-2" onclick="debugConfig()">
                            <i class="ti ti-bug me-2"></i>
                            Debug Config
                        </button>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-2"></i>
                        Save Configuration
                    </button>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('script')
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('.toggle-password').click(function() {
                var input = $(this).siblings('input');
                var icon = $(this).find('i');

                if (input.attr('type') === 'password') {
                    input.attr('type', 'text');
                    icon.removeClass('ti-eye').addClass('ti-eye-off');
                } else {
                    input.attr('type', 'password');
                    icon.removeClass('ti-eye-off').addClass('ti-eye');
                }
            });

            // Form submission
            $('#apiKeyForm').submit(function(e) {
                e.preventDefault();

                var formData = $(this).serialize();

                $.ajax({
                    url: "{{ route('api-keys.update') }}",
                    type: 'PUT',
                    data: formData,
                    beforeSend: function() {
                        $('button[type="submit"]').prop('disabled', true).html(
                            '<i class="ti ti-loader-2 me-2"></i>Saving...'
                        );
                    },
                    success: function(response) {
                        if (response.success) {
                            showToast(response.message, 'success');
                        }
                    },
                    error: function(xhr) {
                        var response = xhr.responseJSON;
                        var message = response.message || 'An error occurred';

                        if (response.errors) {
                            var errorMessages = [];
                            $.each(response.errors, function(field, messages) {
                                errorMessages.push(messages[0]);
                            });
                            message = errorMessages.join('<br>');
                        }

                        showToast(message, 'error');
                    },
                    complete: function() {
                        $('button[type="submit"]').prop('disabled', false).html(
                            '<i class="ti ti-device-floppy me-2"></i>Save Configuration'
                        );
                    }
                });
            });
        });

        // Test API connection
        function testConnection(service, button) {
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="ti ti-loader-2 me-2"></i>Testing...';
            button.disabled = true;

            // Get current form data but exclude _method field (since test should be POST, not PUT)
            var formData = $('#apiKeyForm').serializeArray();

            // Filter out _method field
            formData = formData.filter(function(item) {
                return item.name !== '_method';
            });

            // Convert back to string format and add service and token
            var formDataString = $.param(formData);
            formDataString += '&service=' + service + '&_token=' + $('meta[name="csrf-token"]').attr('content');

            $.ajax({
                url: "{{ route('api-keys.test-connection') }}",
                type: 'POST',
                data: formDataString,
                success: function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                    } else {
                        showToast(response.message || 'Connection test failed', 'error');
                    }
                },
                error: function(xhr) {
                    var response = xhr.responseJSON;
                    var message = 'Connection test failed';

                    if (response && response.message) {
                        message = response.message;
                    } else if (xhr.status === 404) {
                        message = 'Test connection endpoint not found';
                    } else if (xhr.status === 405) {
                        message = 'Method not allowed - please check the request method';
                    } else if (xhr.status === 500) {
                        message = 'Server error occurred during connection test';
                    } else if (xhr.status === 0) {
                        message = 'Network error - please check your connection';
                    }

                    showToast(message, 'error');
                },
                complete: function() {
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            });
        }

        // Refresh configuration cache
        function refreshConfig() {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="ti ti-loader-2 me-2"></i>Refreshing...';
            button.disabled = true;

            $.ajax({
                url: "{{ route('api-keys.refresh-config') }}",
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                    } else {
                        showToast(response.message, 'error');
                    }
                },
                error: function(xhr) {
                    var response = xhr.responseJSON;
                    var message = response.message || 'Failed to refresh cache';
                    showToast(message, 'error');
                },
                complete: function() {
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            });
        }

        // Debug configuration
        function debugConfig() {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="ti ti-loader-2 me-2"></i>Debugging...';
            button.disabled = true;

            $.ajax({
                url: "{{ route('api-keys.debug') }}",
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        // Show detailed debug info
                        let debugInfo = '<div class="text-start">';
                        debugInfo += '<h6>Configuration Status:</h6>';

                        Object.keys(response.data).forEach(service => {
                            const config = response.data[service];
                            debugInfo += `<h6 class="text-uppercase mt-3">${service}:</h6>`;
                            debugInfo += '<ul>';
                            Object.keys(config).forEach(key => {
                                const value = config[key];
                                if (key.includes('_value')) {
                                    debugInfo += `<li><strong>${key}:</strong> ${value || 'Empty'}</li>`;
                                } else if (key.includes('_length')) {
                                    debugInfo += `<li><strong>${key}:</strong> ${value} characters</li>`;
                                } else {
                                    debugInfo += `<li><strong>${key}:</strong> ${value ? 'Configured' : 'Not set'}</li>`;
                                }
                            });
                            debugInfo += '</ul>';
                        });
                        debugInfo += '</div>';

                        Swal.fire({
                            title: 'Debug Configuration',
                            html: debugInfo,
                            icon: 'info',
                            confirmButtonText: 'Close',
                            width: '800px'
                        });
                    } else {
                        showToast(response.message, 'error');
                    }
                },
                error: function(xhr) {
                    var response = xhr.responseJSON;
                    var message = response.message || 'Failed to debug configuration';
                    showToast(message, 'error');
                },
                complete: function() {
                    // Restore button state
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            });
        }

        // Toast notification function using SweetAlert2
        function showToast(message, type) {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
            });

            Toast.fire({
                icon: type,
                title: message
            });
        }
    </script>
@endpush
