<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PageController extends Controller
{
    public function loadContent($page)
    {
        // Ambil semua route yang valid dari menu utama dan submenu
        $menuItems = config('sidebar');
        $allowedPages = [];

        foreach ($menuItems as $item) {
            // Abaikan jika route adalah #
            if (!empty($item['route']) && $item['route'] !== '#') {
                $allowedPages[] = $item['route'];
            }

            // Periksa jika memiliki submenu
            if (!empty($item['submenu']) && is_array($item['submenu'])) {
                foreach ($item['submenu'] as $subItem) {
                    if (!empty($subItem['route']) && $subItem['route'] !== '#') {
                        $allowedPages[] = $subItem['route'];
                    }
                }
            }
        }

        // Jika halaman tidak ditemukan, langsung ke halaman 404
        if (!in_array($page, $allowedPages)) {
            abort(404);
        }
//        return $menuItems;

        return view($page);
    }
}
