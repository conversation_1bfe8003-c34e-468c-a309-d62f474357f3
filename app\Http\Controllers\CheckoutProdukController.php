<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class CheckoutProdukController extends Controller
{
    public function index($slug)
    {
        $product = Product::with(
            [
                'category',
                'images',
                'variants.attributeValues.attribute',
                'wholesalePrices',
                'shippingMethods',
                'paymentMethods',
                'shippingOrigins.shippingAddress',
                // 'responsibleUsers.user'
            ]
        )->where('checkout_url', $slug)->firstOrFail();
        return view('checkout-pengguna.index', compact('product'));
    }
}
