<?php

namespace App\Http\Requests\DataMaster;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PegawaiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('pegawai');
        $rules = [
            'name' => ['required', 'string'],
            'email' => [
                'required',
                // 'email',
                Rule::unique('users')->ignore($id), // Ignore the current user's email during update
            ],
            'password' => $id ? ['nullable', 'string', 'min:5'] : ['required', 'string', 'min:5'], // Password optional during update
            'level_user' => ['required', 'in:' . implode(',', array_keys(LevelUser::toArray()))],
        ];

        if ($this->level_user == LevelUser::TEAM_ADS) {
            $rules['ads_code'] = ['required', 'string'];
            $rules['fbads_id'] = ['nullable', 'string'];
            $rules['fbads_name'] = ['nullable', 'string'];
        } else {
            $rules['ads_code'] = ['nullable', 'string'];
            $rules['fbads_id'] = ['nullable', 'string'];
            $rules['fbads_name'] = ['nullable', 'string'];
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama harus diisi',
            'name.string' => 'Nama harus berupa string',
            'email.required' => 'Email harus diisi',
            'email.email' => 'Email harus berupa email',
            'email.unique' => 'Email sudah terdaftar',
            'password.required' => 'Password harus diisi',
            'password.string' => 'Password harus berupa string',
            'password.min' => 'Password minimal 5 karakter',
            'level_user.required' => 'Level User harus diisi',
            'level_user.in' => 'Level User tidak valid',
        ];
    }
}
