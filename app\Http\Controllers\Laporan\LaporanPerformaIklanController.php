<?php

namespace App\Http\Controllers\Laporan;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Services\Laporan\PerformaIklanService;
use Yajra\DataTables\Facades\DataTables;
use App\Models\User;
use Illuminate\Http\Request;

class LaporanPerformaIklanController extends Controller
{
    public function __construct(protected PerformaIklanService $PerformaIklanService){}

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('laporan.performa-iklan.index', [
            'title' => 'Laporan Performa Iklan',
            'users' => User::where('level_user', 4)->get(),
        ]);        
    }

    public function data(Request $request)
    {                
        $dateRange = $request->input('date'); // Ambil string: "2025-04-01 to 2025-04-30"
        if ($dateRange) {
            [$tanggal_awal, $tanggal_akhir] = explode(' to ', $dateRange);
        } else {
            $tanggal_awal = null;
            $tanggal_akhir = null;
        }        

        $data = $this->PerformaIklanService->grid($tanggal_awal, $tanggal_akhir);
        return DataTables::of($data)
            ->addIndexColumn()            
            ->addColumn('nett_fee_gudang', function ($data) {                                
                return "-";
            })                          
            ->rawColumns(['nett_fee_gudang'])
            ->make(true);
    }
}
