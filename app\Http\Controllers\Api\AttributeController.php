<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductAttribute;
use Illuminate\Http\Request;

class AttributeController extends Controller
{
    /**
     * Store a new attribute or get existing one
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
        ]);

        // Check if attribute already exists
        $attribute = ProductAttribute::firstOrCreate([
            'name' => $request->name
        ]);

        return response()->json([
            'status' => 'success',
            'data' => $attribute
        ]);
    }
}
