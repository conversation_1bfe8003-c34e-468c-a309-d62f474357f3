<?php

namespace App\Enums\Order;

enum OrderStatus: string
{
    case PENDING = 'pending';
    case PROCESS = 'process';
    case DELIVERED = 'delivered';
    case COMPLETED = 'completed';
    case CANCELLED = 'cancelled';
    case RETUR = 'retur';
    case UNPAID = 'unpaid';
    case PAID = 'paid';

    public function badgeClass(): string
    {
        return match ($this) {
            self::PAID => 'text-success bg-light-success',
            self::UNPAID => 'text-danger bg-light-danger',
            self::PENDING => 'text-warning bg-light-warning',
            self::PROCESS => 'text-primary bg-light-primary',
            self::DELIVERED => 'text-info bg-light-info',
            self::COMPLETED => 'text-success bg-light-success',
            self::CANCELLED => 'text-danger bg-light-danger',
            self::RETUR => 'text-secondary bg-light-secondary',
            default => 'text-secondary bg-light-secondary',
        };
    }

    public function toggle()
    {
        return match ($this) {
            self::PENDING => self::COMPLETED,
            self::DELIVERED => self::RETUR,
            self::COMPLETED => self::COMPLETED,
            self::CANCELLED => self::CANCELLED,
            self::RETUR => self::RETUR,
            self::PAID => self::PAID,
            self::UNPAID => self::PAID,
        };
    }
}
