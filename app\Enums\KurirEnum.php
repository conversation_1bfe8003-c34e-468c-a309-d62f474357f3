<?php
// app/Enums/CourierCode.php

namespace App\Enums;

enum KurirEnum: string
{
    case POS = 'pos';
    case WAHANA = 'wahana';
    case JNT = 'jnt';
    case SAP = 'sap';
    case SICEPAT = 'sicepat';
    case JET = 'jet';
    case DSE = 'dse';
    case FIRST = 'first';
    case NINJA = 'ninja';
    case LION = 'lion';
    case IDL = 'idl';
    case REX = 'rex';
    case IDE = 'ide';
    case SENTRAL = 'sentral';
    case ANTERAJA = 'anteraja';
    case JTL = 'jtl';
    case STAR = 'star';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function names(): array
    {
        return array_column(self::cases(), 'name');
    }

    public static function options(): array
    {
        return [
            self::POS->value => 'POS Indonesia',
            self::WAHANA->value => 'Wahana Prestasi Logistik',
            self::JNT->value => 'J&T Express',
            self::SAP->value => 'SAP Express',
            self::SICEPAT->value => 'SiCepat Express',
            self::JET->value => 'JET Express',
            self::DSE->value => '21 Express',
            self::FIRST->value => 'First Logistics',
            self::NINJA->value => 'Ninja Xpress',
            self::LION->value => 'Lion Parcel',
            self::IDL->value => 'IDL Cargo',
            self::REX->value => 'Royal Express Indonesia',
            self::IDE->value => 'ID Express',
            self::SENTRAL->value => 'Sentral Cargo',
            self::ANTERAJA->value => 'AnterAja',
            self::JTL->value => 'JTL Express',
            self::STAR->value => 'Star Cargo',
        ];
    }

    public function label(): string
    {
        return self::options()[$this->value] ?? $this->value;
    }
}
