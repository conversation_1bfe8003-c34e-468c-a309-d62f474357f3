<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Authentic
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        if (Auth::check()) {
            return $next($request);
            // foreach ($roles as $role) {
            //     if ($user->level_user == $role) {
            //         return $next($request);
            //     }
            // }
        } else {
            $url = route('login') . '?next_url=' . $request->path();
            return redirect($url);
            // return $next($request);

        }
        return abort('403');
    }
}
