<?php

namespace App\Http\Controllers\DataMaster;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMaster\PegawaiRequest;
use App\Services\Pegawai\PegawaiService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class PegawaiController extends Controller
{
    public function __construct(protected PegawaiService $pegawaiService){}

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('data-master.pegawai.index', [
            'title' => 'Pegawai'
        ]);
    }

    public function data()
    {
        $users = $this->pegawaiService->grid();
        return DataTables::of($users)
            ->addIndexColumn()
            ->addColumn('level_user', function ($user) {
                return LevelUser::getLabel($user->level_user);
            })
            ->addColumn('action', function ($user) {
                return '
                    <span class="text-info cursor-pointer btn-edit" data-id="' . $user->id . '">
                        <i class="bx bx-message-square-edit icon-md"></i>
                    </span>
                    <span class="text-danger cursor-pointer btn-delete" data-id="' . $user->id . '">
                        <i class="bx bx-trash icon-md"></i>
                    </span>
                ';
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'levelUser' => LevelUser::toArray(),
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PegawaiRequest $request)
    {
        $this->pegawaiService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = $this->pegawaiService->show($id);
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'pegawai' => $user,
            'levelUser' => LevelUser::toArray(),
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PegawaiRequest $request, string $id)
    {
        $this->pegawaiService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->pegawaiService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }
}
