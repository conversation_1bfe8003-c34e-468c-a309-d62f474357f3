/* Edit Mode Specific Styles */
.image-type-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: rgba(40, 167, 69, 0.9);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    z-index: 3;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.image-type-badge.new {
    background: rgba(0, 123, 255, 0.9);
}

/* Image Container Differentiation */
.existing-image {
    border: 2px solid #28a745;
    position: relative;
}

.existing-image::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #28a745, #20c997);
    border-radius: 10px;
    z-index: -1;
    opacity: 0.1;
}

.new-image {
    border: 2px solid #007bff;
    position: relative;
}

.new-image::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #007bff, #6610f2);
    border-radius: 10px;
    z-index: -1;
    opacity: 0.1;
}

/* Primary Badge Enhanced */
.primary-badge {
    position: absolute;
    top: 30px;
    left: 5px;
    background: rgba(255, 193, 7, 0.95);
    color: #212529;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
    z-index: 3;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid rgba(255, 193, 7, 1);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* Enhanced Controls */
.image-preview .controls {
    position: absolute;
    bottom: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
    z-index: 4;
}

.image-preview .controls button {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.image-preview .controls button:hover {
    transform: scale(1.1);
    background: rgba(0, 0, 0, 0.9);
}

.image-preview .controls .set-primary-btn:hover {
    background: rgba(255, 193, 7, 0.9);
    color: #212529;
}

.image-preview .controls .delete-existing-btn:hover,
.image-preview .controls .delete-new-btn:hover {
    background: rgba(220, 53, 69, 0.9);
    color: white;
}

/* Hover Effects for Different Image Types */
.existing-image:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.25);
    border-color: #20c997;
}

.new-image:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.25);
    border-color: #6610f2;
}

/* Image Preview Container Grid Enhancement */
.image-preview-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

/* Sortable Placeholder Enhancement */
.image-preview-placeholder {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 0.875rem;
    text-align: center;
}

.image-preview-placeholder::before {
    content: "Letakkan di sini";
    font-weight: 500;
}

/* Loading States */
.image-preview.loading {
    opacity: 0.6;
    position: relative;
}

.image-preview.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 5;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Indicator */
.upload-progress {
    margin-top: 1rem;
    width: 100%;
    max-width: 400px;
}

.upload-progress .progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
    overflow: hidden;
}

.upload-progress .progress-bar {
    background: linear-gradient(45deg, #007bff, #6610f2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.upload-progress-text {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.upload-progress-text .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    margin-right: 0.5rem;
}

/* Empty State Enhancement */
#empty-images-state {
    transition: all 0.3s ease;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
}

#empty-images-state i {
    transition: all 0.3s ease;
}

#empty-images-state:hover {
    border-color: #adb5bd;
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

#empty-images-state:hover i {
    color: #6c757d !important;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .image-preview-container {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
    }

    .image-type-badge,
    .primary-badge {
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    .image-preview .controls button {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }
}

/* Accessibility Improvements */
.image-preview .controls button:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    border-radius: 6px;
}

.image-preview:hover img {
    transform: scale(1.02);
}

/* Animation for new images being added */
.image-preview.new-addition {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation for images being removed */
.image-preview.removing {
    animation: slideOutDown 0.3s ease-in forwards;
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
}
