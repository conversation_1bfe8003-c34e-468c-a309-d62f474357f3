<?php

namespace App\DataTransferObjects;

class ProductData
{
    /**
     * Extract product data from request
     *
     * @param array $data
     * @return array
     */
    public static function fromRequest(array $data): array
    {
        return [
            'name' => $data['name'],
            'description' => $data['description'] ?? '',
            'category_id' => $data['category_id'],
            'product_code' => $data['product_code'] ?? null,
            'checkout_url' => $data['checkout_url'] ?? null,
            'status' => $data['status'],
            'has_variants' => $data['has_variants'] ?? false,
            'regular_price' => $data['regular_price'],
            'cost_price' => $data['cost_price'] ?? null,
            'has_promo' => $data['has_promo'] ?? false,
            'discount_price' => $data['discount_price'] ?? null,
            'has_wholesale' => $data['has_wholesale'] ?? false,
            'weight' => $data['weight'],
            'length' => $data['length'] ?? null,
            'width' => $data['width'] ?? null,
            'height' => $data['height'] ?? null,
            'advanced_description' => $data['advanced_description']
            // Removed address_selection_type as it's not a column in the products table
            // It's only used for form handling
        ];
    }

    /**
     * Extract wholesale price data from request
     *
     * @param array $data
     * @return array|null
     */
    public static function extractWholesalePrices(array $data): ?array
    {
        if (!isset($data['wholesale_prices']) || !is_array($data['wholesale_prices']) || empty($data['wholesale_prices'])) {
            return null;
        }

        return $data['wholesale_prices'];
    }

    /**
     * Extract variant data from request
     *
     * @param array $data
     * @return array|null
     */
    public static function extractVariants(array $data): ?array
    {
        if (!isset($data['variants']) || !is_array($data['variants']) || empty($data['variants'])) {
            return null;
        }

        return $data['variants'];
    }

    /**
     * Extract shipping origin data from request
     *
     * @param array $data
     * @return array|null
     */
    public static function extractShippingOrigins(array $data): ?array
    {
        if (!isset($data['shipping_origins']) || !is_array($data['shipping_origins']) || empty($data['shipping_origins'])) {
            return null;
        }

        return $data['shipping_origins'];
    }

    /**
     * Extract responsible users data from request
     *
     * @param array $data
     * @return array|null
     */
    public static function extractResponsibleUsers(array $data): ?array
    {
        if (!isset($data['responsible_users']) || !is_array($data['responsible_users']) || empty($data['responsible_users'])) {
            return null;
        }

        return $data['responsible_users'];
    }
}
