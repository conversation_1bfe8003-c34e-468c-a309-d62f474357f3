<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Services\MonitoringCpr\OptimizedDataMonitoringCprService;
use App\Services\CurrencyService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Carbon\Carbon;

class MonitoringCprController extends Controller
{
    public function __construct(
        protected OptimizedDataMonitoringCprService $optimizedCprService,
        protected CurrencyService $currencyService
    ) {
    }

    /**
     * Display the monitoring CPR page
     */
    public function index()
    {
        try {
            $idAkunString = auth()->user()->fbads_id;
            $namaAkunString = auth()->user()->fbads_name;

            if (empty($idAkunString) || empty($namaAkunString)) {
                throw new \Exception('User does not have Facebook Ads account configured');
            }

            $idAkunArray = array_map('trim', explode(',', $idAkunString));
            $namaAkunArray = array_map('trim', explode(',', $namaAkunString));
            $akun_ads = [];

            foreach ($idAkunArray as $key => $id) {
                $akun_ads[] = [
                    'id' => $id,
                    'nama' => $namaAkunArray[$key] ?? "Account {$id}"
                ];
            }

            return view('monitoring-cpr.index', [
                'title' => 'Monitoring CPR',
                'akun' => $akun_ads,
                'exchangeRate' => $this->currencyService->getExchangeRate()
            ]);

        } catch (\Exception $e) {
            Log::error('Error loading monitoring CPR page', [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Gagal memuat halaman monitoring. Silakan hubungi administrator.');
        }
    }

    /**
     * Get data for DataTables - now optimized
     */
    public function data()
    {
        try {
            $idAkun = request()->get('idAkun');

            // Validate inputs
            if (empty($idAkun)) {
                throw new \InvalidArgumentException('ID Akun diperlukan');
            }

            // Ensure account ID has proper format
            $formattedAccountId = strpos($idAkun, 'act_') === 0 ? $idAkun : 'act_' . $idAkun;

            // Get optimized data
            $query = $this->optimizedCprService->getDataForDataTable($formattedAccountId);

            return DataTables::of($query)
                ->addIndexColumn()
                ->editColumn('campaign_name', function ($data) {
                    return $data->campaign_name ?? '-';
                })
                ->editColumn('status', function ($data) {
                    $statusDisplay = $data->status_display;
                    return '<span class="status-indicator ' . $statusDisplay['class'] . '"></span>' . $statusDisplay['label'];
                })
                ->editColumn('results', function ($data) {
                    $results = $data->results ?? 0;
                    return '<strong class="text-primary">' . number_format($results) . '</strong>';
                })
                ->editColumn('cpr', function ($data) {
                    return $data->formatted_cpr;
                })
                ->editColumn('spend', function ($data) {
                    return $data->formatted_spent;
                })
                ->addColumn('cpr_raw', function ($data) {
                    return $data->cpr ?? 0;
                })
                ->addColumn('spend_raw', function ($data) {
                    return $data->spend ?? 0;
                })
                ->addColumn('last_synced_display', function ($data) {
                    return $data->last_synced_at ?
                        '<small class="text-muted">' . $data->last_synced_at->diffForHumans() . '</small>' :
                        '<small class="text-muted">Belum pernah</small>';
                })
                ->addColumn('is_fresh', function ($data) {
                    return $data->is_fresh;
                })
                ->rawColumns(['status', 'results', 'cpr', 'spend', 'last_synced_display'])
                ->make(true);

        } catch (\InvalidArgumentException $e) {
            Log::warning('Invalid parameters for monitoring CPR data', [
                'account_id' => $idAkun ?? null,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Failed to fetch CPR data', [
                'account_id' => $idAkun ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Gagal memuat data. Silakan coba lagi nanti.'
            ], 500);
        }
    }

    /**
     * Fetch and sync campaign data from Facebook Ads - now optimized
     */
    public function create(Request $request)
    {
        try {
            // Check Facebook configuration first
            $config = \App\Models\ApiKey::first();
            if (!$config || empty($config->facebook_app_id) || empty($config->facebook_app_secret) || empty($config->facebook_access_token)) {
                return response()->json([
                    'message' => 'Facebook API is not configured. Please configure Facebook credentials in API settings.',
                    'error' => 'FACEBOOK_NOT_CONFIGURED'
                ], 422);
            }

            // Validate request
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'namaAkun' => 'required|string'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $namaAkun = $validated['namaAkun'];

            Log::info('Starting optimized Facebook Ads data sync', [
                'account_id' => $accountId,
                'account_name' => $namaAkun
            ]);

            // Use optimized service to sync data
            $syncResult = $this->optimizedCprService->syncFromFacebookApi($accountId);

            if (!$syncResult['success']) {
                throw new \Exception($syncResult['message']);
            }

            // Get performance summary
            $defaultDateRange = date('Y-m-01') . ' to ' . date('Y-m-t');
            $performanceSummary = $this->optimizedCprService->getPerformanceSummary($accountId, $defaultDateRange);

            return view('monitoring-cpr.modal', [
                'title' => 'Campaign Data',
                'levelUser' => LevelUser::toArray(),
                'isEditing' => false,
                'namaAkun' => $namaAkun,
                'idAkun' => $accountId,
                'campaign' => $syncResult['data'],
                'exchangeRate' => $this->currencyService->getExchangeRate(),
                'stats' => $syncResult['stats'],
                'summary' => $performanceSummary
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to create/sync campaign data', [
                'account_id' => $request->get('idAkun'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to fetch campaign data. Please try again later.',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get performance summary for dashboard
     */
    public function summary(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'dateRange' => 'required|string'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $dateRange = $validated['dateRange'];

            $summary = $this->optimizedCprService->getPerformanceSummary($accountId, $dateRange);
            $topPerformers = $this->optimizedCprService->getTopPerformingCampaigns($accountId, $dateRange);
            $isDataFresh = $this->optimizedCprService->isDataFresh($accountId, $dateRange);

            return response()->json([
                'success' => true,
                'summary' => $summary,
                'top_performers' => $topPerformers,
                'is_data_fresh' => $isDataFresh,
                'last_updated' => now()->toDateTimeString()
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to get performance summary', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get performance summary'
            ], 500);
        }
    }

    /**
     * Force refresh data from Facebook API
     */
    public function refresh(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'dateRange' => 'nullable|string'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $dateRange = null;

            if (!empty($validated['dateRange'])) {
                $dateRange = explode(' to ', $validated['dateRange']);
            }

            Log::info('Force refreshing Facebook data', [
                'account_id' => $accountId,
                'date_range' => $dateRange
            ]);

            $refreshResult = $this->optimizedCprService->forceRefresh($accountId, $dateRange);

            return response()->json([
                'success' => $refreshResult['success'],
                'message' => $refreshResult['message'],
                'stats' => $refreshResult['stats'] ?? null
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to refresh Facebook data', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clean up old data
     */
    public function cleanup(Request $request)
    {
        try {
            $daysToKeep = $request->get('days', 90);

            $deletedCount = $this->optimizedCprService->cleanupOldData($daysToKeep);

            return response()->json([
                'success' => true,
                'message' => "Successfully cleaned up {$deletedCount} old records",
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cleanup old data', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cleanup old data'
            ], 500);
        }
    }

    /**
     * Sync data with Facebook Ads API
     */
    public function sync(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'namaAkun' => 'required|string',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date'
            ]);

            $accountId = 'act_' . $validated['idAkun'];

            // Use current month as default if no date range provided
            $dateRange = null;
            if (!empty($validated['start_date']) && !empty($validated['end_date'])) {
                $dateRange = [$validated['start_date'], $validated['end_date']];
            }

            Log::info('Starting Facebook Ads data sync', [
                'account_id' => $accountId,
                'date_range' => $dateRange ?? 'current_month_default'
            ]);

            // Use optimized service to sync data
            $syncResult = $this->optimizedCprService->syncFromFacebookApi($accountId, $dateRange);

            if (!$syncResult['success']) {
                throw new \Exception($syncResult['message']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Data berhasil disinkronkan',
                'stats' => $syncResult['stats'] ?? null
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to sync Facebook data', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal menyinkronkan data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Dashboard view
     */
    public function dashboard()
    {
        // Get available accounts (this should come from your account service)
        $akun = []; // Replace with actual account fetching logic

        return view('monitoring-cpr.dashboard', [
            'title' => 'CPR Monitoring Dashboard',
            'akun' => $akun
        ]);
    }

    /**
     * Get enhanced summary with trend data for dashboard
     */
    public function enhancedSummary(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'dateRange' => 'required|string'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $dateRange = $validated['dateRange'];

            // Get basic summary
            $summary = $this->optimizedCprService->getPerformanceSummary($accountId, $dateRange);
            $topPerformers = $this->optimizedCprService->getTopPerformingCampaigns($accountId, $dateRange);
            $isDataFresh = $this->optimizedCprService->isDataFresh($accountId, $dateRange);

            // Get enhanced data for dashboard
            $trendData = $this->optimizedCprService->getTrendData($accountId, $dateRange);
            $statusDistribution = $this->optimizedCprService->getStatusDistribution($accountId, $dateRange);
            $insights = $this->optimizedCprService->getPerformanceInsights($accountId, $dateRange);

            return response()->json([
                'success' => true,
                'summary' => $summary,
                'top_performers' => $topPerformers,
                'is_data_fresh' => $isDataFresh,
                'trend_data' => $trendData,
                'status_distribution' => $statusDistribution,
                'insights' => $insights,
                'last_updated' => now()->toDateTimeString()
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to get enhanced summary', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get enhanced summary'
            ], 500);
        }
    }

    /**
     * Export campaign data to Excel
     */
    public function export(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'dateRange' => 'required|string'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $dateRange = $validated['dateRange'];

            Log::info('Starting CPR data export', [
                'account_id' => $accountId,
                'date_range' => $dateRange
            ]);

            $filepath = $this->optimizedCprService->exportToExcel($accountId, $dateRange);

            if (!file_exists($filepath)) {
                throw new \Exception('Export file could not be created');
            }

            return response()->download($filepath)->deleteFileAfterSend(true);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to export CPR data', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to export data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Compare multiple campaigns
     */
    public function compare(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'campaignIds' => 'required|array|min:2|max:5',
                'campaignIds.*' => 'required|string'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $campaignIds = $validated['campaignIds'];

            $comparison = $this->optimizedCprService->getCampaignComparison($accountId, $campaignIds);

            return response()->json([
                'success' => true,
                'comparison' => $comparison
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to compare campaigns', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to compare campaigns'
            ], 500);
        }
    }

    /**
     * Get real-time data updates
     */
    public function realTimeData(Request $request)
    {
        try {
            $validated = $request->validate([
                'idAkun' => 'required|string',
                'lastUpdate' => 'nullable|date'
            ]);

            $accountId = 'act_' . $validated['idAkun'];
            $lastUpdate = $validated['lastUpdate'] ? Carbon::parse($validated['lastUpdate']) : null;

            // Get latest data since last update
            $query = $this->optimizedCprService->getDataForDataTable($accountId);

            if ($lastUpdate) {
                $query->where('last_synced_at', '>', $lastUpdate);
            }

            $latestData = $query->limit(10)->get();

            return response()->json([
                'success' => true,
                'has_updates' => $latestData->count() > 0,
                'latest_data' => $latestData,
                'timestamp' => now()->toDateTimeString()
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Failed to get real-time data', [
                'request_data' => $request->all(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal mendapatkan data real-time'
            ], 500);
        }
    }
}
