<?php

namespace App\Http\Controllers;

use App\Imports\OrderImport;
use Illuminate\Http\Request;
use App\Enums\Order\OrderStatus;
use App\Enums\Order\PackageType;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Services\Order\OrderService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Artisan;

use Maatwebsite\Excel\Facades\Excel;
use App\Http\Requests\SatuanOngkirRequest;
use App\Services\Order\SatuanOngkirService;
use Intervention\Image\Laravel\Facades\Image;
use Illuminate\Support\Facades\Storage;
use App\Helpers\WooWa;

class OrderController extends Controller
{
    protected SatuanOngkirService $satuanOngkirService;
    public function __construct(protected OrderService $orderService, SatuanOngkirService $satuanOngkirService)
    {
        $this->satuanOngkirService = $satuanOngkirService;
    }
    public function index()
    {
        // Status Pesanan
        $orderStatuses = collect([
            'PENDING' => 'pending',
            'PROCESS' => 'process',
            'DELIVERED' => 'delivered',
            'COMPLETED' => 'completed',
            'CANCELLED' => 'cancelled',
            'RETUR' => 'retur',
        ]);

        // Status Pembayaran
        $paymentStatuses = collect([
            'UNPAID' => 'unpaid',
            'PAID' => 'paid',
        ]);

        // informasi pembayaran
        $paymentMethods = collect([
            'BANK_TRANSFER' => 'Bank Transfer',
            'COD' => 'cod',
        ]);

        return view('order.index', [
            'title' => 'Order',
            'orderStatuses' => $orderStatuses,
            'paymentStatuses' => $paymentStatuses,
            'paymentMethods' => $paymentMethods
        ]);
    }

    /**
     * Get data order
     */
    public function data()
    {
        if (request()->has('date')) {
            $dateRange = request()->get('date');
        }
        $orders = $this->orderService->getOrderIdUnique($dateRange);

        return DataTables::of($orders)
            ->addIndexColumn()
            ->addColumn('checkbox', function ($order) {
                // <input class="form-check-input checkbox-item" type="checkbox" value="' . $order->id . '">
                return '
                        <input type="checkbox" class="row-selector" data-id="' . $order->id . '">
                ';
            })
            ->addColumn('variation', function ($order) {
                $variations = explode(', ', e($order->variation));
                return implode('<br>', $variations);
            })
            ->addColumn('status', function ($order) {
                // binding model Order::$casts dengan enum OrderStatus
                if (in_array(auth()->user()->level_user, [2, 3])) {
                    $badgeClass = ($order->status)->badgeClass();
                    return '
                        <div class="badge rounded-pill ' . $badgeClass . ' p-2 text-uppercase px-3 cursor-pointer btn-status" data-id="' . $order->id . '">
                            <i class="bx bxs-circle align-middle me-1"></i>
                            ' . $order->status->value . '
                        </div>
                    ';
                } else {
                    $badgeClass = ($order->status)->badgeClass();
                    return '
                        <div class="badge rounded-pill ' . $badgeClass . ' p-2 text-uppercase px-3" data-id="' . $order->id . '">
                            <i class="bx bxs-circle align-middle me-1"></i>
                            ' . $order->status->value . '
                        </div>
                    ';
                }
            })
            ->addColumn('payment_status', function ($order) {
                if (in_array(auth()->user()->level_user, [2, 3])) {
                    // binding model dengan enum Order Status
                    $badgeClass = $order->payment_status->badgeClass();
                    return '
                    <div class="badge rounded-pill ' . $badgeClass . ' p-2 text-uppercase px-3 cursor-pointer btn-payment-status" data-id="' . $order->id . '">
                        <i class="bx bxs-circle align-middle me-1"></i>
                        ' . $order->payment_status->value . '
                    </div>
                ';
                } else {
                    $badgeClass = $order->payment_status->badgeClass();
                    return '
                    <div class="badge rounded-pill ' . $badgeClass . ' p-2 text-uppercase px-3" data-id="' . $order->id . '">
                        <i class="bx bxs-circle align-middle me-1"></i>
                        ' . $order->payment_status->value . '
                    </div>
                ';
                }
            })
            ->addColumn('payment_method', function ($order) {
                if ($order->payment_method == 'bank_transfer') {
                    return '
                    <div class="badge rounded-pill bg-primary p-2 text-uppercase px-3 cursor-pointer" data-id="' . $order->id . '">
                        <i class="bx bxs-circle align-middle me-1"></i> BANK TRANSFER
                    </div>
                ';
                } else {
                    return '
                    <div class="badge rounded-pill bg-warning p-2 text-uppercase px-3" data-id="' . $order->id . '">
                        <i class="bx bxs-circle align-middle me-1"></i> COD
                    </div>
                ';
                }
            })
            ->addColumn('action', function ($order) {
                // <span class="text-danger cursor-pointer btn-delete" data-toggle="tooltip" data-placement="top" data-html="true" title="Hapus Data" data-id="' . $order->id . '">
                //     <i class="bx bx-trash icon-md"></i>
                // </span>
                $btn = '';
                if (Auth::user()->level_user == '5' && empty($order->receipt_number)) {
                    $btn .= '
                        <span class="text-info cursor-pointer btn-create-resi" data-toggle="tooltip" data-placement="top" data-html="true" title="Buat Resi" data-id="' . $order->id . '">
                            <i class="bx bx-package icon-md"></i>
                        </span>
                    ';
                }

                if (Auth::user()->level_user == '5' && !empty($order->receipt_number)) {
                    $btn .= '
                        <span class="text-primary cursor-pointer btn-print-resi" data-toggle="tooltip" data-placement="top" data-html="true" title="Cetak Resi" data-id="' . $order->id . '">
                            <i class="bx bx-printer icon-md"></i>
                        </span>
                    ';
                }

                if (Auth::user()->level_user == '3') {
                    $btn .= '<span class="text-warning cursor-pointer btn-create-satuan-ongkir" data-toggle="tooltip" data-placement="top" data-html="true" title="Input Satuan" data-id="' . $order->id . '">
                                <i class="bx bx-pencil icon-md"></i>
                            </span>';
                    if (empty($order->bukti_pembayaran)) {
                        $btn .= '<span class="text-primary cursor-pointer btn-upload-bukti-pembayaran" data-toggle="tooltip"
                        data-placement="top" data-html="true" title="Input Bukti Pembayaran"
                        data-id="' . $order->id . '">
                                <i class="bx bx-camera icon-md"></i>
                            </span>';
                    } else {
                        $btn .= '<span class="text-danger cursor-pointer btn-lihat-bukti-pembayaran" data-toggle="tooltip"
                            data-placement="top" data-html="true" title="Lihat Bukti Pembayaran"
                            data-id="' . $order->id . '" data-bukti-pembayaran="' . $order->bukti_pembayaran . '">
                                    <i class="bx bx-wallet icon-md"></i>
                                </span>';
                    }
                }
                return $btn;
            })
            ->addColumn('follow_up', function ($order) {
                return '
                <button type="button" class="btn btn-outline-success btn-followup-welcome" data-id="' . $order->id . '" ><i class="bx bx-message me-0"></i></button>
            ';
            })
            ->rawColumns(['checkbox', 'action', 'variation', 'status', 'payment_status', 'payment_method', 'receipt_number', 'follow_up'])
            ->make(true);
    }

    public function viewImportExcel()
    {
        return view('order.import', [
            'title' => 'Import Excel'
        ]);
    }

    /**
     * Import excel file
     */
    public function importExcel(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        try {
            // Start the queue worker
            \Artisan::call('queue:work', [
                '--stop-when-empty' => true
            ]);

            Excel::import(new OrderImport, $request->file('file'));
            return back()->with('success', 'File sedang diproses di background!');
        } catch (\Exception $e) {
            return back()->with('error', 'Proses import dihentikan: ' . $e->getMessage());
        }
    }

    public function formCreateResi(Request $request)
    {
        $data = $this->orderService->getDataById($request->id);

        return view('order.modal-create-resi', [
            'title' => 'Create Resi',
            'data' => $data,
            'packageTypes' => PackageType::toArray(),
            'isEditing' => false
        ]);
    }

    public function updateStatus(string $id)
    {
        try {
            $this->orderService->updateStatus($id);
            return response()->json([
                'status' => 'success',
                'message' => 'Status berhasil diubah'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Status gagal diubah: ' . $e->getMessage()
            ]);
        }
    }

    public function updatePaymentStatus(string $id)
    {
        try {
            $this->orderService->updatePaymentStatus($id);

            return response()->json([
                'status' => 'success',
                'message' => 'Status berhasil diubah'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Status gagal diubah: ' . $e->getMessage()
            ]);
        }
    }

    public function formCreateSatuanOngkir(Request $request)
    {
        $data = $this->orderService->getDataById($request->id);
        $odDetail = $this->satuanOngkirService->getDataByOrderId($data->order_id);

        if ($odDetail) {
            $odDetail->payment_method = $data->payment_method;
            return response()->json([
                'status' => 'exists',
                'message' => 'Data sudah ada',
                'data' => $odDetail
            ]);
        }

        return view('order.modal-create-satuan-ongkir', [
            'title' => 'Create Satuan Ongkir',
            'data' => $data,
            'odDetail' => '',
            'packageTypes' => PackageType::toArray(),
            'isEditing' => false
        ]);
    }

    public function createSatuanOngkir(SatuanOngkirRequest $request)
    {
        $odDetail = $this->orderService->getDataById($request->id);
        $validated = $request->validated();
        // Convert formatted currency values
        $validated['harga_satuan'] = $this->cleanCurrency($validated['harga_satuan']);
        $validated['ongkir'] = $this->cleanCurrency($validated['ongkir']);
        $validated['harga_jual'] = $this->cleanCurrency($validated['harga_jual']);

        // 1. total biaya kulak di tf belum sesuai rumus (harga satuan*qty+fee gudang+ongkir)
        // 2. profite belum sesuai rumus (tag konsumen-total biaya kulak)

        if ($request->payment_method == "bank_transfer") {
            $validated['fee_gudang'] = round((($validated['harga_jual'] * 5) / 100), 2);
            $validated['total_biaya_kulak'] = (($validated['harga_satuan'] * $odDetail->quantity) + $validated['fee_gudang'] + $validated['ongkir']);
            $validated['tag_konsumen'] = $this->cleanCurrency($validated['tag_konsumen']);
            $validated['profite'] = $validated['tag_konsumen'] - $validated['total_biaya_kulak'];
        } else {
            $validated['total_biaya_kulak'] = $validated['harga_satuan'] * $odDetail->quantity;
            $validated['biaya_cod'] = round($odDetail->gross_revenue * 3 / 100, 2);
            $validated['ppn_11'] = round((($validated['biaya_cod'] * 11) / 100), 2);
        }

        // Merge validated data into request but return only the array
        $request->merge($validated);
        $data = $request->all();
        $this->satuanOngkirService->store($data);

        return response()->json([
            'success' => true, // Added this line
            'message' => 'Data berhasil disimpan',
            'response' => 'success',
            'data' => $data
        ], 200);
    }

    function cleanCurrency($value)
    {
        // Remove "Rp", spaces, and thousand separators (".")
        $cleaned = preg_replace('/[^\d,]/', '', $value); // Keep only numbers and comma
        return (float) str_replace(',', '.', $cleaned); // Convert comma to dot if needed
    }

    public function bulkUpdate(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:order,id',
            'status' => 'nullable|in:pending,process,delivered,completed,cancelled,retur',
            'payment_status' => 'nullable|in:paid,unpaid',
            'payment_method' => 'nullable|in:bank_transfer,cod'
        ]);

        $ids = $request->input('ids');
        $status = $request->input('status');
        $paymentStatus = $request->input('payment_status');
        $paymentMethod = $request->input('payment_method');

        $updateData = [];
        if ($status) {
            $updateData['status'] = $status;
        }
        if ($paymentStatus) {
            $updateData['payment_status'] = $paymentStatus;
            $updateData['paid_at'] = $paymentStatus === 'paid' ? now() : null;
        }

        if ($paymentMethod) {
            $updateData['payment_method'] = $paymentMethod;
        }

        DB::transaction(function () use ($ids, $updateData) {
            $this->orderService->updateMultiple($ids, $updateData);
        });

        return response()->json([
            'success' => true, // Added this line
            'message' => 'Data berhasil disimpan',
            'response' => 'success',
        ]);
    }

    public function storeUploadBuktiPembayaran(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:order,id',
            'bukti_pembayaran' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        try {
            DB::beginTransaction();

            $order = $this->orderService->getDataById($request->id);

            if ($request->hasFile('bukti_pembayaran')) {
                $file = $request->file('bukti_pembayaran');
                $filename = time() . '_' . $file->getClientOriginalName();
                $filePath = 'uploads/bukti_pembayaran/' . $filename;

                // Compress the image using Intervention Image
                $image = Image::read($file->getRealPath());
                $image->resize(800, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                // Save the compressed image to storage
                $storagePath = storage_path('app/public/' . $filePath);
                $image->save($storagePath, 75); // Save with 75% quality

                // Update the order with the file path
                $order->bukti_pembayaran = 'storage/' . $filePath;
                $order->save();
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bukti pembayaran berhasil diunggah',
                'response' => 'success',
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                'response' => 'error',
            ], 500);
        }
    }

    public function cetakResi(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:order,id'
        ]);

        $order = $this->orderService->getDataById($request->id);

        $transformedData = $this->orderService->transformForReceipt($order);

        if ($transformedData === null) {
            abort(404, 'Nomor resi tidak ditemukan');
        }

        return view('cetak.cetak-resi', [
            'title' => 'Cetak Resi',
            'order' => $transformedData,
        ]);
    }

    public function followUP(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:order,id'
        ]);

        $order = $this->orderService->getDataById($request->id);

        if ($order) {
            // Format nomor HP ke +62xxxxxxxxxxx
            $phone_no = preg_replace('/^0/', '+62', preg_replace('/[^0-9]/', '', $order->phone));
            $cs_name = Auth::user()->name ?? 'Customer Service';
            $message = "Selamat Datang di Toko kami kak {$order->name} ☺️\n\n" .
                "Perkenalkan saya {$cs_name} yang akan membantu kakak.\n" .
                "Saya sudah terima pesanan anda dengan rincian sebagai berikut,\n" .
                "Order ID: {$order->order_id}\n" .
                "Produk: {$order->product_name}, {$order->variation},\n" .
                "              {$order->bump_name}\n" .
                "Harga: {$order->product_price}, {$order->bump_price}\n" .
                "Kode Unik: {$order->unique_code}\n" .
                "Ongkir: {$order->shipping_cost}\n" .
                "Total: {$order->total_price}\n\n" .
                "Dikirim ke:\n" .
                "Nama: {$order->name}\n" .
                "No HP: {$order->phone}\n" .
                "Alamat: {$order->address}\n" .
                "Kota: {$order->city}\n" .
                "Kecamatan: {$order->district}\n\n" .
                "Apakah orderan tersebut sudah benar kak?\n\n" .
                "Jika sudah dipastikan alamat benar, silahkan transfer senilai {$order->total_price}, ke salah satu rekening dibawah ini ya kak:\n" .
                "BCA 6140767997 an ATHIYAH LAILATUN NABILAH\n" .
                "BNI 1921260852 an ATHIYAH LAILATUN NABILAH\n" .
                "BRI 371601000010568  an ATHIYAH LAILATUN NABILAH\n\n" .
                "Jika ingin mengubah metode pembayaran menjadi COD, silahkan balas ini dengan menulis YA, SAYA MAU COD\n" .
                "Saya akan siap membantu memproses transaksinya secepatnya.\n\n" .
                "Terima Kasih.\n" .
                "{$cs_name}";

            // Send message using WooWa with CS name for configuration
            WooWa::sendMessage($phone_no, $message, 'send_message', $cs_name);

            return response()->json([
                'success' => true,
                'message' => 'Pesan berhasil dikirim',
                'response' => 'success',
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Pesan gagal dikirim',
            'response' => 'error',
        ], 500);
    }
}
