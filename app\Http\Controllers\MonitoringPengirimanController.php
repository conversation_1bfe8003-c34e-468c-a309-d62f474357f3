<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use App\Services\KiriminAja\Order\KiriminAjaOrderService;
use App\Services\KiriminAja\Order\InquiryOrderKiriminAjaService;

class MonitoringPengirimanController extends Controller
{
    public function __construct(
        protected InquiryOrderKiriminAjaService $inquiryOrderKiriminAjaService,
        protected KiriminAjaOrderService $kiriminAjaOrderService
    ) {}

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $datas = $this->inquiryOrderKiriminAjaService->grid();
            return DataTables::of($datas)
                ->addIndexColumn()
                ->addColumn('order_id', function ($data) {
                    try {
                        if (is_string($data->packages)) {
                            // Jika $data->packages adalah string JSON, baru decode
                            $details = json_decode($data->packages, true);
                        } else {
                            // Jika sudah dalam bentuk array atau objek, langsung pakai
                            $details = $data->packages;
                        }
                        return $details[0]['order_id'] ?? '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing order_id for row ' . $data->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('destination_address', function ($data) {
                    try {
                        if (is_string($data->packages)) {
                            // Jika $data->packages adalah string JSON, baru decode
                            $details = json_decode($data->packages, true);
                        } else {
                            // Jika sudah dalam bentuk array atau objek, langsung pakai
                            $details = $data->packages;
                        }
                        return $details[0]['destination_address'] ?? '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing destination_address for row ' . $data->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('service', function ($data) {
                    try {
                        if (is_string($data->packages)) {
                            // Jika $data->packages adalah string JSON, baru decode
                            $details = json_decode($data->packages, true);
                        } else {
                            // Jika sudah dalam bentuk array atau objek, langsung pakai
                            $details = $data->packages;
                        }
                        return $details[0]['service'] ?? '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing service for row ' . $data->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('item_name', function ($data) {
                    try {
                        if (is_string($data->packages)) {
                            // Jika $data->packages adalah string JSON, baru decode
                            $details = json_decode($data->packages, true);
                        } else {
                            // Jika sudah dalam bentuk array atau objek, langsung pakai
                            $details = $data->packages;
                        }
                        return $details[0]['item_name'] ?? '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing item_name for row ' . $data->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('action', function ($data) {
                    try {
                        if (is_string($data->packages)) {
                            // Jika $data->packages adalah string JSON, baru decode
                            $details = json_decode($data->packages, true);
                        } else {
                            // Jika sudah dalam bentuk array atau objek, langsung pakai
                            $details = $data->packages;
                        }
                        if (!empty($details[0]['order_id'])) {
                            $btnShow = '<span class="text-info cursor-pointer btn-show" data-toggle="tooltip" data-placement="top" data-html="true" title="Monitor Pengiriman" data-order_id="' . $details[0]['order_id'] . '">
                        <i class="bx bx-show icon-md"></i>
                        </span>';
                        } else {
                            $btnShow = '';
                        }
                        return $btnShow;
                    } catch (\Exception $e) {
                        Log::warning('Error parsing action for row ' . $data->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('monitoring-pengiriman.index', [
            'title' => 'Monitoring Pengiriman',
        ]);
    }

    // monitor pengiriman
    public function lacak(string $id)
    {
        $tracking = $this->kiriminAjaOrderService->trackingOrder(['order_id' => $id]);
        return view('monitoring-pengiriman.modal-monitor-pengiriman', [
            'title' => 'Monitoring Pengiriman',
            'status' => $tracking['status'],
            'histories' => $tracking['histories'] ?? $tracking['text'],
        ]);
    }
}
