<?php

namespace App\Http\Controllers\KiriminAja;

use App\Http\Controllers\Controller;
use App\Services\KiriminAja\Order\KiriminAjaOrderService;
use Illuminate\Http\Request;

class KurirController extends Controller
{
    public function __construct(protected KiriminAjaOrderService $kiriminAjaOrderService) {}

    public function getCurier(Request $request)
    {

        $params = [
            "item_value" => (int) str_replace(['Rp', '.', ' '], '', $request->nilaiBarang),
            "insurance" => 0,
            "origin" => 5647, // Waru, Sidoarjo, Jawa Timur
            "subdistrict_origin" => 71066, // Ningas, Waru, Sidoarjo, Jawa Timur
            "destination" => $request->kecamatan,
            "subdistrict_destination" => $request->desa,
            "length" => $request->panjangBarang,
            "width" => $request->lebarBarang,
            "height" => $request->tinggiBarang,
            "weight" => $request->beratBarang,
            // "courier" => ["jnt", "jne","tiki","posindonesia","paxel","gosend","rpx","borzo","indah","lion","grab_express","jtcargo","sentral","anteraja","jtl","ncs","ncs","sap","idx",]
            "courier" => ["ninja"],
        ];
        $curier = $this->kiriminAjaOrderService->getCurier($params);
        return response()->json($curier);
    }

    public function getSchedules()
    {
        $schedules = $this->kiriminAjaOrderService->getSchedules();
        return response()->json($schedules);
    }
}
