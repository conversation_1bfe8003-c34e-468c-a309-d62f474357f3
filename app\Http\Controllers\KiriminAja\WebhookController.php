<?php

namespace App\Http\Controllers\KiriminAja;

use App\Models\Order;
use App\Models\Retur;
use App\Models\StokRetur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Services\KiriminAja\Area\KiriminAjaAreaService;


class WebhookController extends Controller
{
    public function __construct(protected KiriminAjaAreaService $kiriminAjaAreaService)
    {
    }

    public function webhook()
    {
        $data = request()->all();
        try {
            DB::beginTransaction();

            if ($data['method'] == 'processed_packages') {
                $orderid = str_replace('JUAL-', '', $data['data'][0]['order_id']);
                $order = Order::where('order_id', $orderid)->first();
                if ($order) {
                    $order->receipt_number = $data['data'][0]['awb'];
                    $order->status_shipment = 'processed_packages';
                    $order->save();
                }
            } elseif ($data['method'] == 'shipped_packages') {
                $orderid = str_replace('JUAL-', '', $data['data'][0]['order_id']);
                $order = Order::where('order_id', $orderid)->first();
                if ($order) {
                    $order->status_shipment = 'shipped_packages';
                    $order->save();
                }
            } elseif ($data['method'] == 'canceled_packages') {
                $orderid = str_replace('JUAL-', '', $data['data'][0]['order_id']);
                $order = Order::where('order_id', $orderid)->first();
                if ($order) {
                    $order->status_shipment = 'canceled_packages';
                    $order->save();
                }
            } elseif ($data['method'] == 'finished_packages') {
                $orderid = str_replace('JUAL-', '', $data['data'][0]['order_id']);
                $order = Order::where('order_id', $orderid)->first();
                if ($order->payment_status == 'unpaid') {
                    $order->payment_status = 'paid';
                    $order->status = 'completed';
                    $order->paid_at = now();
                    $order->status_shipment = 'finished_packages';
                    $order->save();
                } else {
                    $order->status = 'completed';
                    $order->status_shipment = 'finished_packages';
                    $order->save();
                }
            } elseif ($data['method'] == 'returned_packages') {
                // $orderid = str_replace('JUAL-', '', $data['data'][0]['order_id']);
                // $order = Order::where('order_id', $orderid)->first();
                // $order->status = 'retur';
                // $order->status_shipment = 'returned_packages';
                // $order->save();
                // if ($order) {
                //     $cekStokRetur = StokRetur::where('product_code', $order->product_code)->first();
                //     if(!empty($cekStokRetur)){
                //         $cekStokRetur->qty = $cekStokRetur->qty + $order->quantity;
                //         $cekStokRetur->sisa_qty = $cekStokRetur->sisa_qty + $order->quantity;;
                //         $cekStokRetur->save();
                //     }else{
                //         $stokretur = new StokRetur();
                //         $stokretur->ads_team_id = 1; // nanti diubah berdasarkan siapa adsnya
                //         $stokretur->product_code = $order->product_code;
                //         $stokretur->product_name = $order->product;
                //         $stokretur->qty = $order->quantity;
                //         $stokretur->sisa_qty = $order->quantity;;
                //         $stokretur->status = 'retur';
                //         $stokretur->save();
                //     }
                // }

                // Validasi data
                if (empty($data['data'][0]['order_id']) || empty($data['data'][0]['awb'])) {
                    Log::warning('Invalid webhook payload', $data);
                    return response()->json(['message' => 'Invalid data'], 400);
                }

                $orderId = str_replace('JUAL-', '', $data['data'][0]['order_id']);
                $order = Order::where('order_id', $orderId)->first();

                if (!$order) {
                    Log::warning("Order dengan ID {$orderId} tidak ditemukan");
                    return response()->json(['message' => 'Order not found'], 404);
                }

                // Update status order
                $order->update([
                    'status' => 'retur',
                    'status_shipment' => 'returned_packages'
                ]);

                // Cek apakah retur sudah ada berdasarkan kode_pesanan atau nomor_resi
                $existingRetur = Retur::where('nomor_resi', $data['data'][0]['awb'])
                    ->first();

                if ($existingRetur) {
                    Log::info("Retur dengan nomor_resi {$data['data'][0]['awb']} sudah ada.");
                    return;
                }

                // Simpan retur baru
                $retur = Retur::create([
                    'kode_pesanan' => $order->order_id,
                    'nama_produk' => $order->product,
                    'kode_produk' => $order->product_code,
                    'variasi' => $order->variation,
                    'jumlah' => $order->quantity,
                    'harga_produk' => $order->product_price,
                    'nama_pelanggan' => $order->name,
                    'telepon_pelanggan' => $order->phone,
                    'alamat_pelanggan' => $order->address,
                    'kurir' => $data['data'][0]['courier'] ?? $order->courier,
                    'nomor_resi' => $data['data'][0]['awb'],
                    'metode_pembayaran' => $order->payment_method,
                    'tanggal_retur' => $data['data'][0]['returned_at'] ?? now(),
                ]);

                Log::info("Retur berhasil disimpan dari webhook returned_packages", [
                    'retur_id' => $retur->id,
                    'order_id' => $order->id
                ]);
            }
            DB::commit();
            return response()->json(['status' => 'success'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Webhook processing failed: ' . $e->getMessage());
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }
}
