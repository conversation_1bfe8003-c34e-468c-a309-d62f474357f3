<?php

namespace App\Http\Requests\DataMaster;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PerhitunganRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tambahan_cod' => ['required','integer'],
            'ppn_cod' => ['required','integer'],
            'fee_gudang_cod' => ['required','integer'],
            'fee_gudang_tf' => ['required','integer'],
            'fee_gudang_retur_cs' => ['required','integer'],
            'fee_gudang_retur_cs_manager' => ['required','integer'],
            'fee_packing_atas' => ['required','integer'],
            'fee_packing_bawah' => ['required','integer'],

        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'tambahan_cod.required' => 'Tambahan COD wajib diisi',
            'tambahan_cod.integer' => 'Tambahan COD harus berupa angka',
            'ppn_cod.required' => 'PPN COD wajib diisi',
            'ppn_cod.integer' => 'PPN COD harus berupa angka',
            'fee_gudang_cod.required' => 'Fee Gudang COD wajib diisi',
            'fee_gudang_cod.integer' => 'Fee Gudang COD harus berupa angka',
            'fee_gudang_tf.required' => 'Fee Gudang TF wajib diisi',
            'fee_gudang_tf.integer' => 'Fee Gudang TF harus berupa angka',
            'fee_gudang_retur_cs.required' => 'Fee Gudang Retur CS wajib diisi',
            'fee_gudang_retur_cs.integer' => 'Fee Gudang Retur CS harus berupa angka',
            'fee_gudang_retur_cs_manager.required' => 'Fee Gudang Retur CS Manager wajib diisi',
            'fee_gudang_retur_cs_manager.integer' => 'Fee Gudang Retur CS Manager harus berupa angka',
            'fee_packing_atas.required' => 'Fee Packing Atas wajib diisi',
            'fee_packing_atas.integer' => 'Fee Packing Atas harus berupa angka',
            'fee_packing_bawah.required' => 'Fee Packing Bawah wajib diisi',
            'fee_packing_bawah.integer' => 'Fee Packing Bawah harus berupa angka',
        ];
    }
}
