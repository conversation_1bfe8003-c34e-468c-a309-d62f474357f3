<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class OptimizedMonitoringCpr extends Model
{
    protected $table = 'optimized_monitoring_cpr';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'campaign_id',
        'campaign_name',
        'account_id',
        'status',
        'campaign_start_date',
        'spend',
        'results',
        'cpr',
        'data_date',
        'last_synced_at',
    ];

    protected $casts = [
        'campaign_start_date' => 'date',
        'data_date' => 'date',
        'last_synced_at' => 'datetime',
        'spend' => 'decimal:4',
        'cpr' => 'decimal:4',
        'results' => 'integer',
    ];

    protected $dates = [
        'campaign_start_date',
        'data_date',
        'last_synced_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Scope untuk filter berdasarkan account ID
     */
    public function scopeForAccount(Builder $query, string $accountId): Builder
    {
        return $query->where('account_id', $accountId);
    }

    /**
     * Scope untuk filter berdasarkan date range
     */
    public function scopeInDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('data_date', [$startDate, $endDate]);
    }

    /**
     * Scope untuk campaign yang masih aktif
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->whereIn('status', ['ACTIVE', 'PAUSED']);
    }

    /**
     * Scope untuk data terbaru (berdasarkan last_synced_at)
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderBy('last_synced_at', 'desc');
    }

    /**
     * Scope untuk campaign dengan performa terbaik (berdasarkan spend)
     */
    public function scopeTopPerformers(Builder $query, int $limit = 10): Builder
    {
        return $query->orderBy('spend', 'desc')->limit($limit);
    }

    /**
     * Accessor untuk formatted spend dalam Rupiah
     */
    public function getFormattedSpentAttribute(): string
    {
        if (!$this->spend || $this->spend == 0) {
            return '<span class="text-muted">-</span>';
        }

        $exchangeRate = config('currency.peso_to_rupiah', 278.62);
        $rupiah = $this->spend * $exchangeRate;

        return '<span class="currency-amount text-success fw-bold">Rp ' . number_format($rupiah, 2, ',', '.') . '</span>';
    }

    /**
     * Accessor untuk formatted CPR dalam Rupiah
     */
    public function getFormattedCprAttribute(): string
    {
        if (!$this->cpr || $this->cpr == 0) {
            return '<span class="text-muted">-</span>';
        }

        $exchangeRate = config('currency.peso_to_rupiah', 278.62);
        $rupiah = $this->cpr * $exchangeRate;

        // Color coding based on CPR performance
        $colorClass = $this->getCprColorClass($rupiah);

        return '<span class="currency-amount fw-bold ' . $colorClass . '">Rp ' . number_format($rupiah, 2, ',', '.') . '</span>';
    }

    /**
     * Get color class based on CPR value
     */
    private function getCprColorClass(float $cprInRupiah): string
    {
        if ($cprInRupiah <= 50000) { // <= 50k IDR
            return 'text-success'; // Green for excellent
        } elseif ($cprInRupiah <= 100000) { // <= 100k IDR
            return 'text-primary'; // Blue for good
        } elseif ($cprInRupiah <= 200000) { // <= 200k IDR
            return 'text-warning'; // Orange for average
        } else {
            return 'text-danger'; // Red for poor
        }
    }

    /**
     * Accessor untuk raw spend dalam USD dengan 4 decimal
     */
    public function getSpendDisplayAttribute(): string
    {
        if (!$this->spend || $this->spend == 0) {
            return '0.0000';
        }

        return number_format($this->spend, 4, '.', ',');
    }

    /**
     * Accessor untuk raw CPR dalam USD dengan 4 decimal
     */
    public function getCprDisplayAttribute(): string
    {
        if (!$this->cpr || $this->cpr == 0) {
            return '0.0000';
        }

        return number_format($this->cpr, 4, '.', ',');
    }

    /**
     * Accessor untuk status display dengan styling
     */
    public function getStatusDisplayAttribute(): array
    {
        $status = $this->status ?? 'UNKNOWN';

        switch (strtoupper($status)) {
            case 'ACTIVE':
                return [
                    'label' => 'Active',
                    'class' => 'status-active',
                    'badge_class' => 'bg-success'
                ];
            case 'PAUSED':
                return [
                    'label' => 'Paused',
                    'class' => 'status-paused',
                    'badge_class' => 'bg-warning'
                ];
            case 'ARCHIVED':
                return [
                    'label' => 'Archived',
                    'class' => 'status-archived',
                    'badge_class' => 'bg-secondary'
                ];
            case 'DELETED':
                return [
                    'label' => 'Deleted',
                    'class' => 'status-deleted',
                    'badge_class' => 'bg-danger'
                ];
            default:
                return [
                    'label' => ucfirst(strtolower($status)),
                    'class' => 'status-unknown',
                    'badge_class' => 'bg-light text-dark'
                ];
        }
    }



    /**
     * Check apakah data masih fresh (dalam 1 jam terakhir)
     */
    public function getIsFreshAttribute(): bool
    {
        return $this->last_synced_at && $this->last_synced_at->diffInHours(now()) < 1;
    }

    /**
     * Get performance rating berdasarkan CPR
     */
    public function getPerformanceRatingAttribute(): string
    {
        if (!$this->cpr || $this->cpr == 0) {
            return 'no-data';
        }

        // Rating berdasarkan CPR (dalam USD, sesuaikan dengan bisnis logic)
        if ($this->cpr <= 5) {
            return 'excellent';
        } elseif ($this->cpr <= 10) {
            return 'good';
        } elseif ($this->cpr <= 20) {
            return 'average';
        } else {
            return 'poor';
        }
    }

    /**
     * Static method untuk bulk update atau create data dengan duplicate handling
     */
    public static function bulkUpdateOrCreate(array $campaignsData, string $accountId, string $dataDate): int
    {
        $updatedCount = 0;

        foreach ($campaignsData as $campaignData) {
            $record = self::updateOrCreate(
                [
                    'campaign_id' => $campaignData['id_kampanye'],
                    'data_date' => $dataDate,
                ],
                [
                    'campaign_name' => $campaignData['nama_kampanye'],
                    'account_id' => $accountId,
                    'status' => strtoupper($campaignData['status'] ?? 'ACTIVE'),
                    'campaign_start_date' => $campaignData['tanggal_mulai'] ?? null,
                    'spend' => $campaignData['biaya_dibelanjakan'] ?? 0,
                    'results' => $campaignData['hasil'] ?? 0,
                    'cpr' => $campaignData['cpr'] ?? 0,
                    'last_synced_at' => now(),
                ]
            );

            if ($record->wasRecentlyCreated || $record->wasChanged()) {
                $updatedCount++;
            }
        }

        return $updatedCount;
    }

    /**
     * Smart update or create with duplicate detection
     */
    public static function smartUpdateOrCreate(array $attributes, array $values): array
    {
        $existing = self::where($attributes)->first();

        if (!$existing) {
            $record = self::create(array_merge($attributes, $values));
            return [
                'record' => $record,
                'action' => 'created',
                'changed' => true
            ];
        }

        // Check for significant changes
        $hasSignificantChange = false;
        $changes = [];

        foreach ($values as $key => $newValue) {
            if ($key === 'last_synced_at')
                continue; // Always update sync time

            $oldValue = $existing->getAttribute($key);

            if ($key === 'spend' || $key === 'cpr') {
                // For monetary values, check if difference is > $0.01
                if (abs($oldValue - $newValue) > 0.01) {
                    $hasSignificantChange = true;
                    $changes[$key] = ['old' => $oldValue, 'new' => $newValue];
                }
            } elseif ($key === 'results') {
                // For results, any change is significant
                if ($oldValue != $newValue) {
                    $hasSignificantChange = true;
                    $changes[$key] = ['old' => $oldValue, 'new' => $newValue];
                }
            } elseif ($oldValue != $newValue) {
                // For other fields, any change is significant
                $hasSignificantChange = true;
                $changes[$key] = ['old' => $oldValue, 'new' => $newValue];
            }
        }

        // Check if data is fresh (less than 1 hour old)
        $isFresh = $existing->last_synced_at && $existing->last_synced_at->diffInHours(now()) < 1;

        if ($isFresh && !$hasSignificantChange) {
            return [
                'record' => $existing,
                'action' => 'skipped',
                'changed' => false,
                'reason' => 'no_significant_change'
            ];
        }

        // Update the record
        $existing->update(array_merge($values, ['last_synced_at' => now()]));

        return [
            'record' => $existing,
            'action' => 'updated',
            'changed' => $hasSignificantChange,
            'changes' => $changes
        ];
    }

    /**
     * Static method untuk mendapatkan data untuk DataTables
     */
    public static function getDataTableData(string $accountId, string $dateRange): Builder
    {
        [$startDate, $endDate] = explode(' to ', $dateRange);

        return self::forAccount($accountId)
            ->inDateRange($startDate, $endDate)
            ->active()
            ->latest()
            ->select([
                'id',
                'campaign_id',
                'campaign_name',
                'status',
                'spend',
                'results',
                'cpr',
                'last_synced_at'
            ]);
    }

    /**
     * Static method untuk clean up data lama
     */
    public static function cleanupOldData(int $daysToKeep = 90): int
    {
        $cutoffDate = Carbon::now()->subDays($daysToKeep);

        return self::where('data_date', '<', $cutoffDate)->delete();
    }

    public static function getDataForDataTable(string $accountId): Builder
    {
        return self::where('account_id', $accountId)
            ->where('status', '!=', 'DELETED')
            ->select([
                'id',
                'campaign_id',
                'campaign_name',
                'status',
                'spend',
                'results',
                'cpr',
                'last_synced_at'
            ]);
    }
}
