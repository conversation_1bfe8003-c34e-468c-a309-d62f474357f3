<?php

namespace App\Http\Controllers;

use Yajra\DataTables\Facades\DataTables;
use App\Services\StokRetur\StokReturService;

class StokController extends Controller
{
    public function __construct(protected StokReturService $stokReturService)
    {
    }

    public function index()
    {
        return view('stok.index', [
            'title' => 'Stok'
        ]);
    }

    public function data()
    {
        $data = $this->stokReturService->getAllWithRetur('layak');

        return DataTables::of($data)
            ->addIndexColumn()
            ->editColumn('nama_produk', fn($row) => $row->retur->nama_produk ?? 'Produk Tidak Ditemukan')
            ->editColumn('kode_produk', fn($row) => $row->retur->kode_produk ?? 'Kode Produk Tidak Ditemukan')
            ->editColumn('ads_team_id', fn($row) => 'N/A')
            ->editColumn('stok_tersedia', fn($row) => $row->jumlah_retur ?? '0')
            ->editColumn('sisa_stok', fn($row) => $row->jumlah_kirim_ulang ?? '0')
            ->addColumn('action', function ($row) {
                return view('components.kirim-ulang-btn', ['id' => $row->id]);
            })
            ->rawColumns(['action'])
            ->make(true);

    }
}
