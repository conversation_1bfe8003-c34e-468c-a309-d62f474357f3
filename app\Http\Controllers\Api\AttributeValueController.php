<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductAttribute;
use App\Models\ProductAttributeValue;
use Illuminate\Http\Request;

class AttributeValueController extends Controller
{
    /**
     * Search attribute values
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        $attribute = $request->get('attribute');
        $attributeId = $request->get('attribute_id');

        $values = ProductAttributeValue::query()
            ->when($query, function ($q) use ($query) {
                return $q->where('value', 'like', "%{$query}%");
            })
            ->when($attribute, function ($q) use ($attribute) {
                return $q->whereHas('attribute', function ($q) use ($attribute) {
                    $q->where('name', $attribute);
                });
            })
            ->when($attributeId, function ($q) use ($attributeId) {
                return $q->where('attribute_id', $attributeId);
            })
            ->with('attribute:id,name')
            ->limit(10)
            ->get();

        return response()->json($values);
    }

    /**
     * Store a new attribute value
     */
    public function store(Request $request)
    {
        $request->validate([
            'value' => 'required|string|max:255',
            'attribute_id' => 'required|exists:product_attributes,id'
        ]);

        // Check if value already exists for this attribute
        $existing = ProductAttributeValue::where('attribute_id', $request->attribute_id)
            ->where('value', $request->value)
            ->first();

        if ($existing) {
            return response()->json([
                'status' => 'exists',
                'data' => $existing
            ]);
        }

        // Create new value
        $value = ProductAttributeValue::create([
            'attribute_id' => $request->attribute_id,
            'value' => $request->value
        ]);

        // Load the attribute relationship
        $value->load('attribute:id,name');

        return response()->json([
            'status' => 'success',
            'data' => $value
        ]);
    }
}
