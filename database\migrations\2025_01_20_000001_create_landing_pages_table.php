<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('landing_pages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->integer('visitors')->default(0);

            // Section 1 - Main Content
            $table->string('product_name')->nullable();
            $table->string('background_color')->default('#DCE8FD');
            $table->string('main_title')->nullable();
            $table->string('sub_title')->nullable();
            $table->string('section_image')->nullable();
            $table->longText('content_description')->nullable();

            // Section 2
            $table->string('section2_product_name')->nullable();
            $table->string('section2_background_color')->default('#DCE8FD');
            $table->string('section2_title')->nullable();
            $table->string('section2_sub_title')->nullable();
            $table->string('section2_image')->nullable();
            $table->longText('section2_content')->nullable();

            // Section 3
            $table->boolean('section3_enabled')->default(false);
            $table->string('section3_product_name')->nullable();
            $table->string('section3_background_color')->default('#DCE8FD');
            $table->string('section3_title')->nullable();
            $table->string('section3_sub_title')->nullable();
            $table->string('section3_image')->nullable();
            $table->longText('section3_content')->nullable();

            // Section 4
            $table->boolean('section4_enabled')->default(false);
            $table->string('section4_product_name')->nullable();
            $table->string('section4_background_color')->default('#DCE8FD');
            $table->string('section4_title')->nullable();
            $table->string('section4_sub_title')->nullable();
            $table->string('section4_image')->nullable();
            $table->longText('section4_content')->nullable();

            // Section 5
            $table->boolean('section5_enabled')->default(false);
            $table->string('section5_product_name')->nullable();
            $table->string('section5_background_color')->default('#DCE8FD');
            $table->string('section5_title')->nullable();
            $table->string('section5_sub_title')->nullable();
            $table->string('section5_image')->nullable();
            $table->longText('section5_content')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('landing_pages');
    }
};
