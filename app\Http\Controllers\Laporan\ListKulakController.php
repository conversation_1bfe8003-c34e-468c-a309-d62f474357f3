<?php

namespace App\Http\Controllers\Laporan;

use App\Enums\KurirEnum;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\ListKulakRequest;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use App\Services\Laporan\ListKulakService;
use App\Services\RajaOngkir\RajaOngkirService;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class ListKulakController extends Controller
{
    public function __construct(
        protected ListKulakService $listKulakService,
        protected RajaOngkirService $rajaOngkirService
    ) {
    }

    /**
     * Display the list kulak index page
     *
     * @return View
     */
    public function index(): View
    {
        return view('laporan.list-kulak.index', [
            'title' => 'Laporan List Kulak',
        ]);
    }

    /**
     * Get list kulak data for DataTables
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function data(Request $request): JsonResponse
    {
        try {
            [$startDate, $endDate] = explode(' to ', $request->date);

            $data = $this->listKulakService->grid($startDate, $endDate);

            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('tanggal_kulak', function ($data) {
                    return $data->tanggal_kulak ? \Carbon\Carbon::parse($data->tanggal_kulak)->format('d-m-Y') : null;
                })
                ->editColumn('nama_produk', function ($data) {
                    $productName = e($data->nama_produk);
                    if ($data->product && $data->product->category) {
                        $productName .= '<br><small class="text-muted">Kategori: ' . e($data->product->category->name) . '</small>';
                    }
                    return $productName;
                })
                ->editColumn('link_produk', function ($data) {
                    if (isset($data->link_produk) && count($data->link_produk) > 1) {
                        $linkList = $data->link_produk;
                        $html = '<ul class="list-unstyled mb-0">';
                        foreach ($linkList as $link) {
                            $html .= '<li><a href="' . $link . '" target="_blank">' . $link . '</a></li>';
                        }
                        $html .= '</ul>';
                        return $html;
                    }
                    return isset($data->link_produk) ? $data->link_produk : null;
                })
                ->addColumn('varian_produk', function ($data) {
                    if (isset($data->varian_produk) && count($data->varian_produk) > 1) {
                        $varianList = $data->varian_produk;
                        $html = '<ul>';
                        foreach ($varianList as $varian) {
                            $html .= '<li>' . $varian . '</li>';
                        }
                        $html .= '</ul>';
                        return $html;
                    }
                    return isset($data->varian_produk) ? $data->varian_produk : null;
                })
                ->addColumn('harga', function ($data) {
                    return isset($data->harga) ? 'Rp ' . number_format((float) $data->harga, 2, '.', ',') : null;
                })
                ->addColumn('harga_reseller', function ($data) {
                    return isset($data->harga_reseller) ? 'Rp ' . number_format((float) $data->harga_reseller, 2, '.', ',') : null;
                })
                ->addColumn('no_resi', function ($data) {
                    return $data->no_resi && $data->no_resi != '-' ?
                        '<button class="btn btn-sm btn-link p-0 text-primary btn-track"
                               data-id="' . e($data->id) . '"
                               title="Lacak Pengiriman">
                               ' . e($data->no_resi) . '
                       </button>'
                        : '<span class="text-muted">Belum ada resi</span>';
                })
                ->addColumn('nama_kurir', function ($data) {
                    return KurirEnum::tryFrom($data->nama_kurir)?->label();
                })
                ->addColumn('total_kulak', function ($data) {
                    return $data->total_kulak ? 'Rp ' . number_format((float) $data->total_kulak, 2, '.', ',') : null;
                })
                ->addColumn('status', function ($data) {
                    $badgeClass = $data->status == 'stok' ? 'bg-primary' : 'bg-warning';
                    return '<span class="badge ' . $badgeClass . '">' . e($data->status) . '</span>';
                })
                ->addColumn('action', function ($data) {
                    return '
                    <div class="d-flex align-items-center gap-2">
                        <button class="btn btn-sm btn-outline-info btn-edit" data-id="' . e($data->id) . '">
                            <i class="bx bx-edit-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-delete" data-id="' . e($data->id) . '">
                            <i class="bx bx-trash"></i>
                        </button>
                    </div>
                    ';
                })
                ->rawColumns(['nama_produk', 'varian_produk', 'link_produk', 'status', 'no_resi', 'action'])
                ->make(true);
        } catch (\Exception $e) {
            Log::error('Error in ListKulakController@data: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat mengambil data'], 500);
        }
    }

    /**
     * Show the form for creating a new list kulak
     *
     * @return View
     */
    public function create(): View
    {
        $products = Product::active()
            ->with(['category', 'variants.attributeValues.attribute'])
            ->orderBy('name')
            ->get();

        return view('laporan.list-kulak.modal', [
            'title' => 'List Kulak',
            'isEditing' => false,
            'kurirs' => KurirEnum::options(),
            'products' => $products
        ]);
    }

    /**
     * Store a newly created list kulak
     *
     * @param ListKulakRequest $request
     * @return JsonResponse
     */
    public function store(ListKulakRequest $request): JsonResponse
    {
        try {
            $validatedData = $request->validated();
            $transformedData = $this->listKulakService->transformData($validatedData);
            $this->listKulakService->store($transformedData);

            return response()->json(['message' => 'Data berhasil disimpan'], 200);
        } catch (\Exception $e) {
            Log::error('Error in ListKulakController@store: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat menyimpan data'], 500);
        }
    }

    /**
     * Show the form for editing the specified list kulak
     *
     * @param string $id
     * @return View
     */
    public function edit(string $id): View
    {
        try {
            $data = $this->listKulakService->show($id);

            $products = Product::active()
                ->with(['category', 'variants.attributeValues.attribute'])
                ->orderBy('name')
                ->get();

            // // Format currency values
            // $data->harga = number_format($data->harga, 0, ',', '.');
            // $data->harga_reseller = number_format($data->harga_reseller, 0, ',', '.');
            // $data->total_kulak = number_format($data->total_kulak, 0, ',', '.');

            return view('laporan.list-kulak.modal', [
                'title' => 'Data List Kulak',
                'data' => $data,
                'kurirs' => KurirEnum::options(),
                'products' => $products,
                'isEditing' => true
            ]);
        } catch (ModelNotFoundException $e) {
            abort(404, 'Data tidak ditemukan');
        }
    }

    /**
     * Update the specified list kulak
     *
     * @param ListKulakRequest $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(ListKulakRequest $request, string $id): JsonResponse
    {
        try {
            $validatedData = $request->validated();

            // Transform the data
            $transformedData = $this->listKulakService->transformData($validatedData);

            // Update the record
            $result = $this->listKulakService->update($id, $transformedData);

            if (!$result) {
                throw new \Exception('Gagal mengupdate data');
            }

            return response()->json([
                'message' => 'Data berhasil diubah',
                'data' => $transformedData
            ], 200);

        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Data tidak ditemukan'], 404);
        } catch (\Exception $e) {
            \Log::error('Error in ListKulakController@update: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat mengubah data: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified list kulak
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $this->listKulakService->delete($id);
            return response()->json(['message' => 'Data berhasil dihapus'], 200);
        } catch (ModelNotFoundException $e) {
            return response()->json(['error' => 'Data tidak ditemukan'], 404);
        } catch (\Exception $e) {
            Log::error('Error in ListKulakController@destroy: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan saat menghapus data'], 500);
        }
    }

    /**
     * Get product details for AJAX request
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getProductDetails(string $id): JsonResponse
    {
        try {
            $product = Product::with([
                'category',
                'variants.attributeValues.attribute',
                'primaryImage'
            ])->findOrFail($id);

            $variants = $product->variants->map(function ($variant) {
                return [
                    'id' => $variant->id,
                    'name' => $variant->variant_name,
                    'price' => $variant->price,
                    'discount_price' => $variant->discount_price,
                    'cost_price' => $variant->cost_price,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $product->id,
                    'name' => $product->name,
                    'category' => $product->category?->name,
                    'regular_price' => $product->regular_price,
                    'discount_price' => $product->discount_price,
                    'cost_price' => $product->cost_price,
                    'current_price' => $product->current_price,
                    'checkout_url' => $product->checkout_url,
                    'variants' => $variants,
                    'has_variants' => $product->has_variants,
                ]
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Produk tidak ditemukan'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Error in ListKulakController@getProductDetails: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil data produk'
            ], 500);
        }
    }

    /**
     * Track shipping status
     *
     * @param string $id
     * @return View
     */
    public function lacak(string $id): View
    {
        try {
            $listKulak = $this->listKulakService->show($id);

            if (!$listKulak->no_resi) {
                throw new \Exception('Nomor resi tidak tersedia');
            }

            $trackingData = $this->rajaOngkirService->trackWaybill(
                $listKulak->no_resi,
                KurirEnum::tryFrom($listKulak->nama_kurir)->value
            );

            return view('laporan.list-kulak.lacak-pengiriman', [
                'data' => $trackingData
            ]);
        } catch (ModelNotFoundException $e) {
            abort(404, 'Data tidak ditemukan');
        } catch (\Exception $e) {
            Log::error('Error in ListKulakController@lacak: ' . $e->getMessage());
            abort(500, 'Terjadi kesalahan saat melacak pengiriman');
        }
    }
}
