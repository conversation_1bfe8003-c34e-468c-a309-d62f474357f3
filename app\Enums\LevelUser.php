<?php

namespace App\Enums;

enum LevelUser: int
{
    case ADMIN = 1;
    case CS_MANAJER = 2;
    case CS = 3;
    case TEAM_ADS = 4;
    case GUDANG = 5;
    case SHIPMENT = 6;

    /**
     * Mengembalikan label yang lebih deskriptif untuk setiap peran.
     *
     * @return string
     */
    public function label(): string
    {
        return match ($this) {
            self::ADMIN => 'Admin',
            self::CS_MANAJER => 'CS Manajer',
            self::CS => 'CS',
            self::TEAM_ADS => 'Tim ADS',
            self::GUDANG => 'Gudang',
            self::SHIPMENT => 'Shipment',
        };
    }

    /**
     * Mengembalikan semua peran sebagai array asosiatif (ID => Label).
     *
     * @return array
     */
    public static function toArray(): array
    {
        return [
            self::ADMIN->value => self::ADMIN->label(),
            self::CS_MANAJER->value => self::CS_MANAJER->label(),
            self::CS->value => self::CS->label(),
            self::TEAM_ADS->value => self::TEAM_ADS->label(),
            self::GUDANG->value => self::GUDANG->label(),
            self::SHIPMENT->value => self::SHIPMENT->label(),
        ];
    }

    public static function getLabel(int $value): string
    {
        return self::tryFrom($value)?->label() ?? 'Unknown';
    }
}
