<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class XSS
{
    /**
     * Fields that should be excluded from HTML stripping (allow HTML content)
     */
    protected $excludedFields = [
        'content_description',
        'section2_content',
        'section3_content',
        'section4_content',
        'section5_content',
        'advanced_description', // For products
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $userInput = $request->all();

        array_walk_recursive($userInput, function (&$userInput, $key) {
            // Skip HTML stripping for specific content fields that need to allow HTML
            if (in_array($key, $this->excludedFields)) {
                // For content fields, only remove potentially dangerous tags but keep formatting
                $userInput = $this->sanitizeHtmlContent($userInput);
            } else {
                // For other fields, strip all HTML tags as before
                $userInput = strip_tags($userInput);
            }
        });

        $request->merge($userInput);
        return $next($request);
    }

    /**
     * Sanitize HTML content while preserving safe formatting tags
     *
     * @param string $content
     * @return string
     */
    protected function sanitizeHtmlContent($content)
    {
        if (!is_string($content)) {
            return $content;
        }

        // List of allowed HTML tags for content formatting
        $allowedTags = '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><blockquote><code><pre><span><div>';

        // Strip dangerous tags but keep formatting tags
        $sanitized = strip_tags($content, $allowedTags);

        // Remove potentially dangerous attributes
        $sanitized = preg_replace('/(<[^>]+)\s+(on\w+|javascript:|vbscript:|data:)[^>]*>/i', '$1>', $sanitized);

        // Remove script and style content
        $sanitized = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $sanitized);
        $sanitized = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $sanitized);

        return $sanitized;
    }
}
