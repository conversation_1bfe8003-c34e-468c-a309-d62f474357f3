<?php

namespace App\Services;

class CurrencyService
{
    /**
     * Convert Philippine Peso to Indonesian Rupiah
     */
    public function convertPesoToRupiah(float $peso): float
    {
        $exchangeRate = config('currency.peso_to_rupiah', 278.62);
        return $peso * $exchangeRate;
    }

    /**
     * Format currency as Indonesian Rupiah
     */
    public function formatRupiah(float $amount, bool $showDecimals = false): string
    {
        $decimals = $showDecimals ? 2 : 0;
        return 'Rp ' . number_format($amount, $decimals, ',', '.');
    }

    /**
     * Get current exchange rate
     */
    public function getExchangeRate(): float
    {
        return config('currency.peso_to_rupiah', 278.62);
    }

    /**
     * Convert and format peso to rupiah in one step
     */
    public function convertAndFormatPesoToRupiah(float $peso, bool $showDecimals = false): string
    {
        $rupiah = $this->convertPesoToRupiah($peso);
        return $this->formatRupiah($rupiah, $showDecimals);
    }
}
