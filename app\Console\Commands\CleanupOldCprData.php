<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MonitoringCpr\OptimizedDataMonitoringCprService;

class CleanupOldCprData extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cpr:cleanup {--days=90 : Number of days to keep}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old CPR monitoring data to maintain database performance';

    /**
     * Execute the console command.
     */
    public function handle(OptimizedDataMonitoringCprService $cprService)
    {
        $daysToKeep = (int) $this->option('days');

        $this->info("Starting cleanup of CPR data older than {$daysToKeep} days...");

        try {
            $deletedCount = $cprService->cleanupOldData($daysToKeep);

            $this->info("✅ Successfully cleaned up {$deletedCount} old CPR records.");

            if ($deletedCount > 0) {
                $this->line("Database size has been optimized.");
            } else {
                $this->line("No old records found to clean up.");
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Failed to cleanup old CPR data: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
