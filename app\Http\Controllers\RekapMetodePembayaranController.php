<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMaster\PegawaiRequest;
use App\Services\Rekap\MetodePembayaranService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class RekapMetodePembayaranController extends Controller
{
    public function __construct(protected MetodePembayaranService $metodePembayaranService)
    {
    }

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('rekap.metode-pembayaran.index', [
            'title' => 'Rekap Metode Pembayaran'
        ]);
    }

    public function data()
    {
        // $status = 'bank_transfer';
        if (request()->has('paymentMethod')) {
            $status = request()->get('paymentMethod');
        }
        if (request()->has('date')) {
            $dateRange = request()->get('date');
        }
        $data = $this->metodePembayaranService->grid($status, $dateRange);

        return DataTables::of($data)
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'levelUser' => LevelUser::toArray(),
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PegawaiRequest $request)
    {
        $this->metodePembayaranService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = $this->metodePembayaranService->show($id);
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'pegawai' => $user,
            'levelUser' => LevelUser::toArray(),
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PegawaiRequest $request, string $id)
    {
        $this->metodePembayaranService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->metodePembayaranService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }
}
