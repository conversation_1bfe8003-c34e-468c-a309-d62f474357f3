<?php

namespace App\Http\Controllers;

use App\Models\InquiryOrderKiriminAja;
use App\Models\NotifyKiriminAja;
use App\Models\User;
use App\Services\KiriminAja\Area\KiriminAjaAreaService;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class DataController extends Controller
{
    public function __construct(protected KiriminAjaAreaService $kiriminAjaAreaService){}

    public function index()
    {
        $prov = $this->kiriminAjaAreaService->getSubDistricts(1);
        return response()->json($prov);
    }

    public function addressByName()
    {

        $prov = $this->kiriminAjaAreaService->addressByName('Neglasari');
        return $prov;
    }

    public function test(Request $request)
    {
        $model = NotifyKiriminAja::whereJsonContains('response->status', true)->get();
        return response()->json($model->load('inquiryOrderKiriminAja'));
    }

}
