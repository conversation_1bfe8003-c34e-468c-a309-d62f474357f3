<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ApiKey extends Model
{
    use HasFactory;

    protected $fillable = [
        'rajaongkir_endpoint',
        'rajaongkir_api_key',
        'kiriminaja_env',
        'kiriminaja_dev_base_url',
        'kiriminaja_dev_api_key',
        'kiriminaja_prod_base_url',
        'kiriminaja_prod_api_key',
        'kiriminaja_base_uri',
        'facebook_app_id',
        'facebook_app_secret',
        'facebook_access_token',
        'woowa_phone_num',
        'woowa_base_url',
        'woowa_api_key',
    ];

    protected $casts = [
        'rajaongkir_api_key' => 'encrypted',
        'kiriminaja_dev_api_key' => 'encrypted',
        'kiriminaja_prod_api_key' => 'encrypted',
        'facebook_app_id' => 'encrypted',
        'facebook_app_secret' => 'encrypted',
        'facebook_access_token' => 'encrypted',
        'woowa_api_key' => 'encrypted',
    ];

    /**
     * Get the first API key configuration (singleton pattern)
     */
    public static function getConfig()
    {
        return self::first() ?? new self();
    }

    /**
     * Update or create API key configuration
     */
    public static function updateConfig(array $data)
    {
        $config = self::first();

        if ($config) {
            $config->update($data);
        } else {
            $config = self::create($data);
        }

        return $config;
    }
}
