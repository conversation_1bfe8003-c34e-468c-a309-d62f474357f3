<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cs_woowa_configurations', function (Blueprint $table) {
            $table->dropColumn('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cs_woowa_configurations', function (Blueprint $table) {
            $table->text('notes')->nullable()->comment('Catatan tambahan untuk konfigurasi')->after('is_active');
        });
    }
};
