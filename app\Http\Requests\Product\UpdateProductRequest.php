<?php

namespace App\Http\Requests\Product;

use App\Helpers\CurrencyHelper;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     * Convert Indonesian formatted currency values to standard format.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Process price fields
        $priceFields = ['regular_price', 'discount_price', 'cost_price'];

        foreach ($priceFields as $field) {
            if ($this->has($field) && is_string($this->input($field))) {
                try {
                    $parsedValue = CurrencyHelper::parseIndonesianNumber($this->input($field));
                    Log::debug("Parsed {$field}: " . $this->input($field) . " -> {$parsedValue}");
                    $this->merge([$field => $parsedValue]);
                } catch (\Exception $e) {
                    Log::error("Error parsing {$field}: " . $e->getMessage());
                }
            }
        }

        // Process variant prices
        if ($this->has('variants') && is_array($this->input('variants'))) {
            $variants = $this->input('variants');

            foreach ($variants as $key => $variant) {
                if (isset($variant['price']) && is_string($variant['price'])) {
                    $variants[$key]['price'] = CurrencyHelper::parseIndonesianNumber($variant['price']);
                }

                if (isset($variant['discount_price']) && is_string($variant['discount_price'])) {
                    $variants[$key]['discount_price'] = CurrencyHelper::parseIndonesianNumber($variant['discount_price']);
                }
            }

            $this->merge(['variants' => $variants]);
        }

        // Process wholesale prices
        if ($this->has('wholesale_prices') && is_array($this->input('wholesale_prices'))) {
            $wholesalePrices = $this->input('wholesale_prices');

            foreach ($wholesalePrices as $key => $price) {
                if (isset($price['price']) && is_string($price['price'])) {
                    $wholesalePrices[$key]['price'] = CurrencyHelper::parseIndonesianNumber($price['price']);
                }
            }

            $this->merge(['wholesale_prices' => $wholesalePrices]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $productId = $this->route('product');

        return [
            // Basic Info
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'advanced_description' => 'required|string',
            'category_id' => 'required|exists:product_categories,id',
            'product_code' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('products', 'product_code')->ignore($productId)
            ],
            'checkout_url' => 'nullable|string|max:255',
            'status' => 'required|in:draft,active',
            'primary_image' => 'nullable|boolean',
            'primary_image_index' => 'nullable|numeric',
            'has_variants' => 'nullable|boolean',

            // Pricing
            'regular_price' => 'required_if:has_variants,0|numeric|min:0|max:1000000000',
            'cost_price' => 'nullable|numeric|min:0|max:1000000000',
            'has_promo' => 'nullable|boolean',
            'discount_price' => 'nullable|numeric|min:0|max:1000000000',
            'has_wholesale' => 'nullable|boolean',

            // Dimensions & Weight
            'weight' => 'required|numeric|min:0',
            'length' => 'nullable|numeric|min:0',
            'width' => 'nullable|numeric|min:0',
            'height' => 'nullable|numeric|min:0',

            // Shipping & Payment Methods
            'shipping_methods' => 'required|array|min:1',
            'shipping_methods.*' => 'exists:shipping_methods,id',
            'payment_methods' => 'required|array|min:1',
            'payment_methods.*' => 'exists:payment_methods,id',

            // Address Selection
            'address_selection_type' => 'required|in:saved,new',
            'selected_address_id' => 'required_if:address_selection_type,saved|exists:shipping_addresses,id',

            // Responsible Users
            'distribution_type' => [
                'required',
                Rule::in(['equal', 'percentage', 'fixed'])
            ],
            'responsible_users' => [
                'required',
                'array',
                'min:1'
            ],
            'responsible_users.*' => [
                'exists:users,id,level_user,3' // Pastikan user adalah customer service (level 3)
            ],
            'distribution_values' => [
                'required_if:distribution_type,percentage,fixed',
                'array',
                function ($attribute, $value, $fail) {
                    // Validasi khusus berdasarkan tipe distribusi
                    $type = $this->input('distribution_type');

                    if ($type === 'percentage') {
                        $total = array_sum($value);
                        if (abs($total - 100) > 0.01) {
                            $fail('Total persentase distribusi harus tepat 100%.');
                        }

                        foreach ($value as $userId => $percentage) {
                            if ($percentage <= 0 || $percentage > 100) {
                                $fail("Persentase untuk user ID {$userId} harus antara 0-100%.");
                            }
                        }
                    }

                    if ($type === 'fixed') {
                        foreach ($value as $userId => $fixedValue) {
                            if ($fixedValue < 1) {
                                $fail("Nilai tetap untuk user ID {$userId} harus minimal 1.");
                            }
                        }
                    }
                }
            ],

            'distribution_values.*' => [
                'numeric',
                'min:0.01'
            ],

            // Product Images
            'product_images' => 'nullable|array',
            'product_images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'primary_image_id' => 'nullable|exists:product_images,id',

            // Conditional validation for wholesale prices
            'wholesale_prices' => 'required_if:has_wholesale,1|array|min:1',
            'wholesale_prices.*.id' => 'nullable|exists:product_wholesale_prices,id',
            'wholesale_prices.*.min_quantity' => 'required_if:has_wholesale,1|integer|min:1',
            'wholesale_prices.*.price' => 'required_if:has_wholesale,1|numeric|min:0|max:1000000000',

            // Conditional validation for shipping origins
            'shipping_origins' => 'required_if:address_selection_type,new|array|min:1',
            'shipping_origins.*.id' => 'nullable|exists:product_shipping_origins,id',
            'shipping_origins.*.name' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.address' => 'required_if:address_selection_type,new|string',
            'shipping_origins.*.city' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.state' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.country' => 'required_if:address_selection_type,new|string|max:255',
            'shipping_origins.*.postal_code' => 'required_if:address_selection_type,new|string|max:20',

            // Conditional validation for variants
            'variant_attributes' => 'required_if:has_variants,1|array|min:1',
            'variant_values' => 'required_if:has_variants,1|array',
            'variants' => 'required_if:has_variants,1|array|min:1',
            'variants.*.id' => 'nullable|exists:product_variants,id',
            'variants.*.combination' => 'required_if:has_variants,1|string',
            'variants.*.price' => 'required|numeric|min:0|max:1000000000',
            'variants.*.discount_price' => 'nullable|numeric|min:0|max:1000000000',
            'variants.*.variant_code' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('product_variants', 'product_code')->where(function ($query) use ($productId) {
                    return $query->where('product_id', '!=', $productId);
                })
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'shipping_methods.required' => 'Pilih setidaknya satu metode pengiriman',
            'shipping_methods.array' => 'Format metode pengiriman tidak valid',
            'shipping_methods.min' => 'Pilih setidaknya satu metode pengiriman',
            'payment_methods.required' => 'Pilih setidaknya satu metode pembayaran',
            'payment_methods.array' => 'Format metode pembayaran tidak valid',
            'payment_methods.min' => 'Pilih setidaknya satu metode pembayaran',
            'distribution_type.required' => 'Tipe distribusi wajib dipilih.',
            'distribution_type.in' => 'Tipe distribusi tidak valid.',
            'responsible_users.required' => 'Pilih setidaknya satu pengguna yang bertanggung jawab',
            'responsible_users.array' => 'Format pengguna tidak valid',
            'responsible_users.min' => 'Pilih setidaknya satu pengguna yang bertanggung jawab',
            'responsible_users.*.exists' => 'Pengguna yang dipilih tidak valid atau bukan Customer Service.',
            'distribution_values.required_if' => 'Nilai distribusi wajib diisi untuk tipe ini.',
            'distribution_values.array' => 'Format nilai distribusi tidak valid.',
            'distribution_values.*.numeric' => 'Nilai distribusi harus berupa angka.',
            'distribution_values.*.min' => 'Nilai distribusi minimal :min.',
            'regular_price.required_if' => 'Harga normal wajib diisi untuk produk tanpa variasi.',
            'regular_price.numeric' => 'Harga normal harus angka.',
            'regular_price.min' => 'Harga normal minimal 0.',
            'regular_price.max' => 'Harga normal maksimal 1 miliar.',
            'cost_price.numeric' => 'Harga pokok harus angka.',
            'cost_price.min' => 'Harga pokok minimal 0.',
            'cost_price.max' => 'Harga pokok maksimal 1 miliar.',
            'discount_price.numeric' => 'Harga promo harus angka.',
            'discount_price.min' => 'Harga promo minimal 0.',
            'discount_price.max' => 'Harga promo maksimal 1 miliar.',
            'wholesale_prices.*.price.numeric' => 'Harga grosir harus angka.',
            'wholesale_prices.*.price.min' => 'Harga grosir minimal 0.',
            'wholesale_prices.*.price.max' => 'Harga grosir maksimal 1 miliar.',
            'variants.*.price.numeric' => 'Harga variasi harus angka.',
            'variants.*.price.min' => 'Harga variasi minimal 0.',
            'variants.*.price.max' => 'Harga variasi maksimal 1 miliar.',
            'variants.*.discount_price.numeric' => 'Harga promo variasi harus angka.',
            'variants.*.discount_price.min' => 'Harga promo variasi minimal 0.',
            'variants.*.discount_price.max' => 'Harga promo variasi maksimal 1 miliar.',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $type = $this->input('distribution_type');
            $users = $this->input('responsible_users', []);
            $values = $this->input('distribution_values', []);

            // Validasi kesesuaian antara selected users dan distribution values
            if ($type !== 'equal' && count($users) !== count($values)) {
                $validator->errors()->add(
                    'distribution_values',
                    'Jumlah nilai distribusi harus sama dengan jumlah penanggung jawab.'
                );
            }

            // Pastikan semua user yang dipilih memiliki nilai distribusi
            if ($type !== 'equal') {
                foreach ($users as $userId) {
                    if (!array_key_exists($userId, $values)) {
                        $validator->errors()->add(
                            'distribution_values',
                            "User ID {$userId} tidak memiliki nilai distribusi."
                        );
                        break;
                    }
                }
            }
        });
    }
}
