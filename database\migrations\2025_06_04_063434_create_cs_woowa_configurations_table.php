<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cs_woowa_configurations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('woowa_phone_num')->nullable();
            $table->string('woowa_base_url')->default('https://notifapi.com');
            $table->text('woowa_api_key')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable()->comment('Catatan tambahan untuk konfigurasi');
            $table->timestamp('last_tested_at')->nullable()->comment('Waktu terakhir test connection');
            $table->json('last_test_result')->nullable()->comment('Hasil test connection terakhir');
            $table->timestamps();

            // Index untuk performa
            $table->index(['user_id', 'is_active']);
            $table->unique(['user_id']); // Satu CS hanya boleh punya satu konfigurasi aktif
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cs_woowa_configurations');
    }
};
