<?php

namespace App\Http\Controllers\Laporan;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMaster\PegawaiRequest;
use App\Services\Laporan\FeeGudangService;
use Yajra\DataTables\Facades\DataTables;
use App\Models\User;
use Illuminate\Http\Request;

class FeeGudangCODController extends Controller
{
    public function __construct(protected FeeGudangService $FeeGudangService){}

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('laporan.fee-gudang-cod.index', [
            'title' => 'Laporan Fee Gudang COD',
        ]);
    }

    public function data(Request $request)
    {                

        $dateRange = $request->input('date'); // Ambil string: "2025-04-01 to 2025-04-30"
        if ($dateRange) {
            [$tanggal_awal, $tanggal_akhir] = explode(' to ', $dateRange);
        } else {
            $tanggal_awal = null;
            $tanggal_akhir = null;
        }        
        $jenis = 'cod';

        $data = $this->FeeGudangService->grid($tanggal_awal, $tanggal_akhir, $jenis);
        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('fee_packing', function ($data) {
                if ($data->gross_revenue >= '130000') {
                    return '6000';
                }else{
                    return '4000';
                }                        
            })
            ->addColumn('fee_gudang', function ($data) {                
                return isset($data->harga_jual) ? ($data->harga_jual * 12.5) : '0';
            })                        
            ->addColumn('nett_fee_gudang', function ($data) {                
                $fee_packing = ($data->gross_revenue >= 130000) ? 6000 : 4000; 
                $fee_gudang = ($data->harga_jual) ? ($data->harga_jual * 12.5 / 100) : 0;
                return $fee_gudang - $fee_packing;
            })                          
            ->rawColumns(['fee_packing','fee_gudang','nett_fee_gudang'])
            ->make(true);
    }
}
