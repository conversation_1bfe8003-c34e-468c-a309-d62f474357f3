<?php

namespace App\Http\Requests\DataMaster;

use App\Enums\LevelUser;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EarningPotentialRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {        
        $rules = [            
            'nama' => ['required', 'string'],
            'total_orders' => ['required', 'numeric'],
            'lead_tf' => ['required', 'numeric'],
            'lead_cod' => ['required', 'numeric'],
            'total_profit_tf' => ['required', 'numeric'],
            'total_profit_cod' => ['required', 'numeric'],
            'total_order_tf' => ['required', 'numeric'],
            'total_order_cod' => ['required', 'numeric'],
            'closer_ratio_day' => ['required', 'numeric'],
            'iklan' => ['required', 'string'],
            'cpr' => ['required', 'numeric'],
            'return' => ['required', 'numeric'],
            'expense' => ['nullable', 'string'],
            'ads_code' => ['required', 'string'],
        ];
        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Nama harus diisi',
            'name.string' => 'Nama harus berupa string',
            'email.required' => 'Email harus diisi',
            'email.email' => 'Email harus berupa email',
            'email.unique' => 'Email sudah terdaftar',
            'password.required' => 'Password harus diisi',
            'password.string' => 'Password harus berupa string',
            'password.min' => 'Password minimal 5 karakter',
            'level_user.required' => 'Level User harus diisi',
            'level_user.in' => 'Level User tidak valid',
        ];
    }
}
