/* Monitoring CPR Module Styles */
.filter-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-active {
    background-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
    animation: pulse-green 2s infinite;
}

.status-paused {
    background-color: #ffc107;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

.status-inactive,
.status-archived,
.status-deleted {
    background-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.status-unknown {
    background-color: #6c757d;
    box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.2);
}

/* Enhanced <PERSON>urrency Styling */
.currency-amount {
    font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
    font-weight: 600;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    display: inline-block;
}

.currency-amount:hover {
    transform: scale(1.05);
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* CPR Performance Colors */
.text-success {
    color: #198754 !important;
}

.text-primary {
    color: #0d6efd !important;
}

.text-warning {
    color: #fd7e14 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Table Enhancements */
.table-dark th {
    background: linear-gradient(135deg, #495057 0%, #343a40 100%) !important;
    border-color: #495057 !important;
    color: #fff !important;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* Campaign Name Styling */
.campaign-name {
    font-weight: 500;
    color: #2c3e50;
    max-width: 280px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: help;
    transition: color 0.2s ease;
}

.campaign-name:hover {
    color: #007bff;
}

/* Number Formatting */
.text-center strong {
    font-size: 1.1rem;
    font-weight: 700;
}

/* Animations */
@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .currency-amount {
        font-size: 0.85rem;
        font-weight: 500;
    }

    .campaign-name {
        max-width: 150px;
        font-size: 0.9rem;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

.btn-fetch-data {
    min-width: 150px;
    transition: all 0.2s ease;
}

.btn-fetch-data:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.8;
}

.currency-amount {
    font-weight: 600;
    color: #2c5aa0;
}

.campaign-name {
    text-align: left !important;
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Table Styles */
.table-responsive {
    margin: 0;
    border-radius: 8px;
    overflow: hidden;
}

.table tbody tr:hover {
    background-color: rgba(0,0,0,0.02);
}

/* Loading States */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}

/* Accessibility */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .filter-section {
        padding: 1rem;
    }

    .table-responsive {
        margin: 0 -1rem;
    }

    .btn-fetch-data {
        min-width: auto;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Toast Container */
.toast-container {
    z-index: 1050;
}

/* Form Validation */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}
