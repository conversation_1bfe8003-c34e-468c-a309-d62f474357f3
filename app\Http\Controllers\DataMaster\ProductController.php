<?php

namespace App\Http\Controllers\DataMaster;

use App\Models\User;
use App\Models\PaymentMethod;
use App\Models\ShippingMethod;
use App\Models\ShippingAddress;
use App\Models\ProductShippingOrigin;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\DataTransferObjects\ProductData;
use App\Http\Requests\Product\StoreProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Services\Product\ProductService;
use Illuminate\Support\Facades\Log;
use Yajra\DataTables\Facades\DataTables;

class ProductController extends Controller
{
    public function __construct(protected ProductService $productService)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('data-master.product.index', [
            'title' => 'Produk',
            'categories' => $this->productService->getAllCategories(),
        ]);
    }

    /**
     * Process DataTables ajax request.
     */
    public function data(Request $request)
    {
        $filters = $request->only(['status', 'category_id', 'search']);
        $products = $this->productService->grid($filters)->get();

        return DataTables::of($products)
            ->addIndexColumn()
            ->addColumn('category_name', function ($product) {
                return $product->category->name ?? '-';
            })
            ->addColumn('price', function ($product) {
                // If product has variants, show price range
                if ($product->has_variants && $product->variants->count() > 0) {
                    // Get min and max prices from variants
                    $prices = $product->variants->pluck('price')->filter()->toArray();

                    if (count($prices) > 0) {
                        $minPrice = min($prices);
                        $maxPrice = max($prices);

                        if ($minPrice == $maxPrice) {
                            $html = '<div class="fw-bold">Rp ' . number_format($minPrice, 0, ',', '.') . '</div>';
                        } else {
                            $html = '<div class="fw-bold">Rp ' . number_format($minPrice, 0, ',', '.') . ' - Rp ' . number_format($maxPrice, 0, ',', '.') . '</div>';
                        }

                        // Check for discount prices in variants
                        $discountPrices = $product->variants->pluck('discount_price')->filter()->toArray();
                        if (count($discountPrices) > 0) {
                            $minDiscountPrice = min($discountPrices);
                            $maxDiscountPrice = max($discountPrices);

                            if ($minDiscountPrice == $maxDiscountPrice) {
                                $html .= '<div class="text-danger small">Diskon: Rp ' . number_format($minDiscountPrice, 0, ',', '.') . '</div>';
                            } else {
                                $html .= '<div class="text-danger small">Diskon: Rp ' . number_format($minDiscountPrice, 0, ',', '.') . ' - Rp ' . number_format($maxDiscountPrice, 0, ',', '.') . '</div>';
                            }
                        }
                    } else {
                        // Fallback to regular price if no variant prices
                        $html = '<div class="fw-bold">Rp ' . number_format($product->regular_price, 0, ',', '.') . '</div>';

                        if ($product->discount_price) {
                            $html .= '<div class="text-danger small">Diskon: Rp ' . number_format($product->discount_price, 0, ',', '.') . '</div>';
                        }
                    }
                } else {
                    // Regular product without variants
                    $html = '<div class="fw-bold">Rp ' . number_format($product->regular_price, 0, ',', '.') . '</div>';

                    if ($product->discount_price) {
                        $html .= '<div class="text-danger small">Diskon: Rp ' . number_format($product->discount_price, 0, ',', '.') . '</div>';
                    }
                }

                // Show wholesale indicator if product has wholesale prices
                if ($product->has_wholesale) {
                    $html .= '<div class="badge bg-info text-white mt-1">Grosir</div>';
                }

                return $html;
            })
            ->addColumn('status_badge', function ($product) {
                if ($product->status === 'active') {
                    return '<span class="badge bg-success">Aktif</span>';
                } else {
                    return '<span class="badge bg-secondary">Tidak Aktif</span>';
                }
            })
            ->addColumn('image', function ($product) {
                $image = $product->primaryImage;
                if ($image) {
                    // Use the URL accessor from the model which now returns the correct URL
                    return '<div class="product-image-container">
                              <img src="' . $image->url . '" class="img-fluid rounded" style="width: 70px; height: 70px; object-fit: cover;">
                           </div>';
                }

                // Return a placeholder if no image is available
                return '<div class="text-center">
                          <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                            <i class="bx bx-image text-secondary" style="font-size: 24px;"></i>
                          </div>
                        </div>';
            })
            ->addColumn('product_code', function ($product) {
                return $product->product_code ? '<span class="badge bg-light text-dark">' . $product->product_code . '</span>' : '-';
            })
            ->addColumn('variants_count', function ($product) {
                if ($product->has_variants) {
                    return '<span class="badge bg-info">' . $product->variants->count() . ' varian</span>';
                }
                return '<span class="badge bg-secondary">Tidak ada</span>';
            })
            ->addColumn('action', function ($product) {
                return view('data-master.product.partials.action-buttons', ['product' => $product])->render();
            })
            ->rawColumns(['price', 'status_badge', 'image', 'product_code', 'variants_count', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get shipping addresses for the current user
        $shippingAddresses = ShippingAddress::where('user_id', auth()->id())
            ->orderBy('is_default', 'desc') // Use is_default as the column has been renamed
            ->orderBy('created_at', 'desc')
            ->get();

        return view('data-master.product.create', [
            'title' => 'Tambah Produk',
            'categories' => $this->productService->getAllCategories(),
            'shippingMethods' => ShippingMethod::active()->orderBy('name', 'asc')->get(),
            'paymentMethods' => PaymentMethod::active()->orderBy('name', 'asc')->get(),
            'users' => User::where('level_user', 3)->orderBy('name', 'asc')->get(), // CS users
            'attributes' => $this->productService->getAllAttributes(), // Product attributes for variants
            'shippingAddresses' => $shippingAddresses,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProductRequest $request)
    {
        // dd($request->all());
        ;
        try {
            Log::info('Starting product creation process');

            // Get validated data
            $validated = $request->validated();

            // Additional validation for price based on product type
            $this->validatePriceBasedOnType($validated);

            // Extract product data using DTO
            $productData = ProductData::fromRequest($validated);

            // Check if product has variants and validate price consistency
            if (
                isset($validated['has_variants']) && $validated['has_variants'] &&
                isset($validated['variants']) && count($validated['variants']) > 0
            ) {
                // Get min and max prices from variants
                $variantPrices = collect($validated['variants'])->pluck('price')->filter()->toArray();

                if (count($variantPrices) > 0) {
                    $minPrice = min($variantPrices);
                    $maxPrice = max($variantPrices);

                    // If all variant prices are the same, sync with regular price
                    if ($minPrice == $maxPrice && $minPrice != $validated['regular_price']) {
                        // Update the regular price to match the variant price
                        $productData['regular_price'] = $minPrice;

                        // Log the price synchronization
                        Log::info('Synchronized new product regular price with variant price', [
                            'old_price' => $validated['regular_price'],
                            'new_price' => $minPrice
                        ]);
                    }
                }
            }

            // Create the product
            $product = $this->productService->create($productData);

            // Handle product images
            $this->handleProductImages($request, $product->id, $validated);

            // Handle variants if needed
            $this->handleProductVariants($product, $validated);

            // Handle wholesale prices if provided
            $this->handleWholesalePrices($product->id, $validated);

            // Handle shipping and payment methods
            $this->productService->updateShippingMethods($product->id, $validated['shipping_methods']);
            $this->productService->updatePaymentMethods($product->id, $validated['payment_methods']);

            // Handle shipping origins
            $this->handleShippingOrigins($product->id, $validated);

            // Handle responsible users
            $this->handleResponsibleUsers($product->id, $validated);

            Log::info('Product created successfully', ['product_id' => $product->id]);

            return redirect()->route('data-master.product.index')
                ->with('success', 'Produk berhasil ditambahkan.');
        } catch (\Exception $e) {
            Log::error('Failed to store product: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal menambahkan produk: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            // Load product with all relationships
            $product = $this->productService->find($id);

            if (!$product) {
                return redirect()->route('data-master.product.index')
                    ->with('error', 'Produk tidak ditemukan');
            }

            // Get shipping addresses for the current user
            $shippingAddresses = ShippingAddress::where('user_id', auth()->id())
                ->orderBy('is_default', 'desc') // Use is_default as the column has been renamed
                ->orderBy('created_at', 'desc')
                ->get();

            // Set the primary image
            $primaryImage = $product->images->where('is_primary', true)->first();
            if ($primaryImage) {
                $product->primary_image_id = $primaryImage->id;
            }

            // Prepare variant attributes and values if product has variants
            if ($product->has_variants && $product->variants->count() > 0) {
                // Extract attribute data from variants using attributeValues relation
                $variantAttributes = [];
                $variantValues = [];
                $attributeValueIds = [];

                // Process variants to extract attributes and values
                foreach ($product->variants as $variant) {
                    // Make sure attributeValues are loaded with their attributes
                    if ($variant->attributeValues->isEmpty()) {
                        $variant->load('attributeValues.attribute');
                    }

                    // Use attributeValues relation
                    foreach ($variant->attributeValues as $attributeValue) {
                        $attributeName = $attributeValue->attribute->name;
                        $value = $attributeValue->value;

                        if (!in_array($attributeName, $variantAttributes)) {
                            $variantAttributes[] = $attributeName;
                        }

                        if (!isset($variantValues[$attributeName])) {
                            $variantValues[$attributeName] = [];
                            $attributeValueIds[$attributeName] = [];
                        }

                        if (!in_array($value, $variantValues[$attributeName])) {
                            $variantValues[$attributeName][] = $value;
                            $attributeValueIds[$attributeName][$value] = $attributeValue->id;
                        }
                    }

                    // Also process combination for backward compatibility and to ensure we have all data
                    $combination = json_decode($variant->combination, true);
                    if (is_array($combination)) {
                        foreach ($combination as $attribute => $value) {
                            if (!in_array($attribute, $variantAttributes)) {
                                $variantAttributes[] = $attribute;
                            }

                            if (!isset($variantValues[$attribute])) {
                                $variantValues[$attribute] = [];
                                $attributeValueIds[$attribute] = [];
                            }

                            if (!in_array($value, $variantValues[$attribute])) {
                                $variantValues[$attribute][] = $value;

                                // Try to find the attribute value ID if it's not already set
                                if (!isset($attributeValueIds[$attribute][$value])) {
                                    // Find the attribute
                                    $attributeModel = \App\Models\ProductAttribute::where('name', $attribute)->first();
                                    if ($attributeModel) {
                                        // Find the attribute value
                                        $attributeValueModel = \App\Models\ProductAttributeValue::where('attribute_id', $attributeModel->id)
                                            ->where('value', $value)
                                            ->first();

                                        if ($attributeValueModel) {
                                            $attributeValueIds[$attribute][$value] = $attributeValueModel->id;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // Ensure the variant has a complete combination JSON that matches attributeValues
                    if (empty($combination) || count($combination) !== count($variant->attributeValues)) {
                        $newCombination = [];
                        foreach ($variant->attributeValues as $attributeValue) {
                            $newCombination[$attributeValue->attribute->name] = $attributeValue->value;
                        }

                        if (!empty($newCombination)) {
                            try {
                                // Try to save the combination, but handle the case where the column doesn't exist
                                $variant->combination = json_encode($newCombination);
                                $variant->save();
                            } catch (\Exception $e) {
                                // If the column doesn't exist, just log it and continue
                                // The combination data is already stored in the attribute values relationship
                                Log::warning('Could not save combination JSON to variant: ' . $e->getMessage(), [
                                    'variant_id' => $variant->id,
                                    'combination' => $newCombination
                                ]);
                            }
                        }
                    }
                }

                $product->variant_attributes = $variantAttributes;
                $product->variant_values = $variantValues;
                $product->attribute_value_ids = $attributeValueIds;

                // Log for debugging
                Log::info('Product variant data prepared for edit form', [
                    'product_id' => $product->id,
                    'variant_attributes' => $variantAttributes,
                    'variant_values' => $variantValues,
                    'attribute_value_ids' => $attributeValueIds
                ]);
            }

            return view('data-master.product.edit', [
                'title' => "Edit Produk: {$product->name}",
                'product' => $product,
                'categories' => $this->productService->getAllCategories(),
                'shippingMethods' => ShippingMethod::active()->orderBy('name', 'asc')->get(),
                'paymentMethods' => PaymentMethod::active()->orderBy('name', 'asc')->get(),
                'users' => User::where('level_user', 3)->orderBy('name', 'asc')->get(), // CS users
                'attributes' => $this->productService->getAllAttributes(), // Product attributes for variants
                'shippingAddresses' => $shippingAddresses,
            ]);
        } catch (\Exception $e) {
            Log::error('Error loading product edit form: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('data-master.product.index')
                ->with('error', 'Terjadi kesalahan saat memuat form edit: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProductRequest $request, string $id)
    {
        try {
            Log::info('Starting product update process for ID: ' . $id);

            $product = $this->productService->find($id);
            if (!$product) {
                return redirect()->route('data-master.product.index')
                    ->with('error', 'Produk tidak ditemukan');
            }

            // Get validated data
            $validated = $request->validated();

            // Additional validation for price based on product type
            $this->validatePriceBasedOnType($validated);

            // Extract product data using DTO
            $productData = ProductData::fromRequest($validated);

            // Check if product has variants and validate price consistency
            if ($product->has_variants && isset($validated['variants']) && count($validated['variants']) > 0) {
                // Get min and max prices from variants
                $variantPrices = collect($validated['variants'])->pluck('price')->filter()->toArray();

                if (count($variantPrices) > 0) {
                    $minPrice = min($variantPrices);
                    $maxPrice = max($variantPrices);

                    // If all variant prices are the same, sync with regular price
                    if ($minPrice == $maxPrice && $minPrice != $validated['regular_price']) {
                        // Update the regular price to match the variant price
                        $productData['regular_price'] = $minPrice;

                        // Log the price synchronization
                        Log::info('Synchronized product regular price with variant price', [
                            'product_id' => $id,
                            'old_price' => $validated['regular_price'],
                            'new_price' => $minPrice
                        ]);
                    }
                }
            }

            // Update the product
            $this->productService->update($id, $productData);

            // Handle product images
            $this->handleProductImages($request, $id, $validated);

            // Handle primary image update if requested
            if ($request->has('primary_image_id')) {
                $this->productService->setPrimaryImage($id, $request->input('primary_image_id'));
            }

            // Handle variants if needed
            $this->handleProductVariantsUpdate($product, $validated);

            // Handle wholesale prices
            $this->handleWholesalePricesUpdate($product, $validated);

            // Handle shipping and payment methods
            $this->productService->updateShippingMethods($id, $validated['shipping_methods']);
            $this->productService->updatePaymentMethods($id, $validated['payment_methods']);

            // Handle shipping origins
            $this->handleShippingOriginsUpdate($product, $validated);

            // Handle responsible users
            $this->handleResponsibleUsersUpdate($product, $validated);

            Log::info('Product updated successfully', ['product_id' => $id]);

            return redirect()->route('data-master.product.index')
                ->with('success', 'Produk berhasil diperbarui.');
        } catch (\Exception $e) {
            Log::error('Failed to update product: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal memperbarui produk: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $product = $this->productService->find($id);

            if (!$product) {
                return redirect()->route('data-master.product.index')
                    ->with('error', 'Produk tidak ditemukan');
            }

            return view('data-master.product.show', [
                'title' => $product->name,
                'product' => $product,
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to show product: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('data-master.product.index')
                ->with('error', 'Gagal menampilkan produk: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $product = $this->productService->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Produk tidak ditemukan'
                ], 404);
            }

            $this->productService->delete($id);

            return response()->json([
                'success' => true,
                'message' => 'Produk berhasil dihapus'
            ], 200);
        } catch (\Exception $e) {
            \Log::error('Failed to delete product: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus produk: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle product images upload
     *
     * @param Request $request
     * @param int $productId
     * @param array $validated
     * @return void
     */
    private function handleProductImages(Request $request, int $productId, array $validated): void
    {
        if ($request->hasFile('product_images')) {
            $primaryImageIndex = $validated['primary_image_index'] ?? 0;
            foreach ($request->file('product_images') as $index => $file) {
                $isPrimary = $index == $primaryImageIndex;
                $this->productService->uploadProductImage($productId, $file, $isPrimary);
            }
        }
    }

    /**
     * Handle product variants creation
     *
     * @param object $product
     * @param array $validated
     * @return void
     */
    private function handleProductVariants(object $product, array $validated): void
    {
        if ($product->has_variants && isset($validated['variants'])) {
            // Log the incoming variant data for debugging
            Log::info('Processing variants creation', [
                'product_id' => $product->id,
                'variants_count' => count($validated['variants'])
            ]);

            foreach ($validated['variants'] as $variantData) {
                $variantToSave = [
                    'combination' => $variantData['combination'],
                    'product_code' => $variantData['variant_code'] ?? null,
                    'price' => $variantData['price'],
                    'discount_price' => $variantData['discount_price'] ?? null,
                ];

                // Initialize attributeValueIds array
                $attributeValueIds = [];

                // Process attribute_value_ids if available
                if (isset($variantData['attribute_value_ids']) && is_array($variantData['attribute_value_ids'])) {
                    $attributeValueIds = array_filter($variantData['attribute_value_ids'], fn($id) => !empty($id));

                    Log::info('Using attribute_value_ids from request', [
                        'variant_code' => $variantToSave['product_code'],
                        'attribute_value_ids' => $attributeValueIds
                    ]);
                }

                // Also process attribute_values if available
                if (isset($variantData['attribute_values']) && is_array($variantData['attribute_values'])) {
                    foreach ($variantData['attribute_values'] as $attributeName => $attributeValue) {
                        // Find the attribute
                        $attribute = \App\Models\ProductAttribute::where('name', $attributeName)->first();
                        if ($attribute) {
                            // Find the attribute value
                            $value = \App\Models\ProductAttributeValue::where('attribute_id', $attribute->id)
                                ->where('value', $attributeValue)
                                ->first();

                            if ($value) {
                                if (!in_array($value->id, $attributeValueIds)) {
                                    $attributeValueIds[] = $value->id;
                                }
                            } else {
                                // Create the attribute value if it doesn't exist
                                $newValue = \App\Models\ProductAttributeValue::create([
                                    'attribute_id' => $attribute->id,
                                    'value' => $attributeValue
                                ]);
                                $attributeValueIds[] = $newValue->id;
                            }
                        }
                    }

                    Log::info('Processed attribute_values from request', [
                        'variant_code' => $variantToSave['product_code'],
                        'attribute_values' => $variantData['attribute_values'],
                        'resulting_ids' => $attributeValueIds
                    ]);
                }

                // If we still don't have attribute value IDs, try to extract them from the combination
                if (empty($attributeValueIds) && isset($variantData['combination'])) {
                    $combination = json_decode($variantData['combination'], true);

                    if (is_array($combination)) {
                        foreach ($combination as $attributeName => $attributeValue) {
                            $attribute = \App\Models\ProductAttribute::where('name', $attributeName)->first();
                            if ($attribute) {
                                $value = \App\Models\ProductAttributeValue::where('attribute_id', $attribute->id)
                                    ->where('value', $attributeValue)
                                    ->first();

                                if ($value) {
                                    $attributeValueIds[] = $value->id;
                                } else {
                                    // Create the attribute value if it doesn't exist
                                    $newValue = \App\Models\ProductAttributeValue::create([
                                        'attribute_id' => $attribute->id,
                                        'value' => $attributeValue
                                    ]);
                                    $attributeValueIds[] = $newValue->id;
                                }
                            }
                        }

                        Log::info('Extracted attribute value IDs from combination', [
                            'variant_code' => $variantToSave['product_code'],
                            'combination' => $combination,
                            'resulting_ids' => $attributeValueIds
                        ]);
                    }
                }

                // Ensure we have attribute value IDs before saving
                if (empty($attributeValueIds)) {
                    Log::warning('No attribute value IDs found for variant', [
                        'variant_code' => $variantToSave['product_code'],
                        'combination' => $variantData['combination'] ?? null
                    ]);
                } else {
                    Log::info('Adding product variant with attribute value IDs', [
                        'variant_code' => $variantToSave['product_code'],
                        'attribute_value_ids' => $attributeValueIds
                    ]);
                }

                // Create the variant with attribute value IDs
                $variant = $this->productService->addProductVariant($product->id, $variantToSave, $attributeValueIds);

                // Verify that the variant was created with attribute values
                if ($variant) {
                    $savedAttributeValueIds = $variant->attributeValues()->pluck('product_attribute_values.id')->toArray();
                    Log::info('Variant created with attribute values', [
                        'variant_id' => $variant->id,
                        'saved_attribute_value_ids' => $savedAttributeValueIds
                    ]);

                    // If no attribute values were saved, try to attach them directly
                    if (empty($savedAttributeValueIds) && !empty($attributeValueIds)) {
                        Log::warning('No attribute values were saved, attempting to attach directly', [
                            'variant_id' => $variant->id,
                            'attribute_value_ids' => $attributeValueIds
                        ]);

                        try {
                            $variant->attributeValues()->attach($attributeValueIds);
                            Log::info('Attribute values attached directly', [
                                'variant_id' => $variant->id,
                                'attribute_value_ids' => $attributeValueIds
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Failed to attach attribute values directly', [
                                'variant_id' => $variant->id,
                                'error' => $e->getMessage()
                            ]);
                        }
                    }
                }
            }
        }
    }

    /**
     * Handle product variants update
     *
     * @param object $product
     * @param array $validated
     * @return void
     */
    private function handleProductVariantsUpdate(object $product, array $validated): void
    {
        if ($product->has_variants && isset($validated['variants'])) {
            // Log the incoming variant data for debugging
            Log::info('Processing variants update', [
                'product_id' => $product->id,
                'variants_count' => count($validated['variants'])
            ]);

            // Process existing variants
            $existingVariantIds = collect($validated['variants'])
                ->filter(fn($variant) => isset($variant['id']) && $variant['id'] > 0)
                ->pluck('id')
                ->toArray();

            // Delete variants not in the request
            foreach ($product->variants as $variant) {
                if (!in_array($variant->id, $existingVariantIds)) {
                    $this->productService->deleteProductVariant($variant->id);
                }
            }

            // Update or create variants
            foreach ($validated['variants'] as $variantData) {
                // Use attribute_value_ids if available, otherwise try to find IDs from attribute_values
                $attributeValueIds = [];

                if (isset($variantData['attribute_value_ids'])) {
                    $attributeValueIds = array_filter($variantData['attribute_value_ids'], fn($id) => !empty($id));

                    // Log attribute value IDs for debugging
                    Log::info('Using attribute_value_ids from request', [
                        'attribute_value_ids' => $attributeValueIds
                    ]);

                    // Remove from variantData to avoid database errors
                    unset($variantData['attribute_value_ids']);
                }

                // Also process attribute_values if available
                if (isset($variantData['attribute_values']) && is_array($variantData['attribute_values'])) {
                    foreach ($variantData['attribute_values'] as $attributeName => $attributeValue) {
                        // Find the attribute
                        $attribute = \App\Models\ProductAttribute::where('name', $attributeName)->first();
                        if ($attribute) {
                            // Find the attribute value
                            $value = \App\Models\ProductAttributeValue::where('attribute_id', $attribute->id)
                                ->where('value', $attributeValue)
                                ->first();

                            if ($value) {
                                if (!in_array($value->id, $attributeValueIds)) {
                                    $attributeValueIds[] = $value->id;
                                }
                            } else {
                                // Create the attribute value if it doesn't exist
                                $newValue = \App\Models\ProductAttributeValue::create([
                                    'attribute_id' => $attribute->id,
                                    'value' => $attributeValue
                                ]);
                                $attributeValueIds[] = $newValue->id;
                            }
                        }
                    }

                    // Log attribute values processing
                    Log::info('Processed attribute_values from request', [
                        'attribute_values' => $variantData['attribute_values'],
                        'resulting_ids' => $attributeValueIds
                    ]);

                    // Remove attribute_values from variantData to avoid database errors
                    unset($variantData['attribute_values']);
                }

                // Process combination data if available
                if (isset($variantData['combination']) && is_string($variantData['combination'])) {
                    $combination = json_decode($variantData['combination'], true);

                    if (is_array($combination) && !empty($combination) && empty($attributeValueIds)) {
                        // If we don't have attribute value IDs yet, try to get them from the combination
                        foreach ($combination as $attributeName => $attributeValue) {
                            $attribute = \App\Models\ProductAttribute::where('name', $attributeName)->first();
                            if ($attribute) {
                                $value = \App\Models\ProductAttributeValue::where('attribute_id', $attribute->id)
                                    ->where('value', $attributeValue)
                                    ->first();

                                if ($value) {
                                    $attributeValueIds[] = $value->id;
                                } else {
                                    // Create the attribute value if it doesn't exist
                                    $newValue = \App\Models\ProductAttributeValue::create([
                                        'attribute_id' => $attribute->id,
                                        'value' => $attributeValue
                                    ]);
                                    $attributeValueIds[] = $newValue->id;
                                }
                            }
                        }

                        // Log combination processing
                        Log::info('Processed combination data', [
                            'combination' => $combination,
                            'resulting_ids' => $attributeValueIds
                        ]);
                    }
                }

                if (isset($variantData['id']) && $variantData['id'] > 0) {
                    // Update existing variant
                    $variant = $this->productService->updateProductVariant($variantData['id'], $variantData, $attributeValueIds);

                    Log::info('Updated existing variant', [
                        'variant_id' => $variantData['id'],
                        'attribute_value_ids' => $attributeValueIds
                    ]);

                    // Verify that the variant was updated with attribute values
                    if ($variant) {
                        $savedAttributeValueIds = $variant->attributeValues()->pluck('product_attribute_values.id')->toArray();
                        Log::info('Variant updated with attribute values', [
                            'variant_id' => $variant->id,
                            'saved_attribute_value_ids' => $savedAttributeValueIds
                        ]);

                        // If no attribute values were saved, try to attach them directly
                        if (empty($savedAttributeValueIds) && !empty($attributeValueIds)) {
                            Log::warning('No attribute values were saved during update, attempting to attach directly', [
                                'variant_id' => $variant->id,
                                'attribute_value_ids' => $attributeValueIds
                            ]);

                            try {
                                // Detach any existing values first to avoid duplicates
                                $variant->attributeValues()->detach();
                                $variant->attributeValues()->attach($attributeValueIds);

                                Log::info('Attribute values attached directly during update', [
                                    'variant_id' => $variant->id,
                                    'attribute_value_ids' => $attributeValueIds
                                ]);
                            } catch (\Exception $e) {
                                Log::error('Failed to attach attribute values directly during update', [
                                    'variant_id' => $variant->id,
                                    'error' => $e->getMessage()
                                ]);
                            }
                        }
                    }
                } else {
                    // Create new variant
                    $newVariant = $this->productService->addProductVariant($product->id, $variantData, $attributeValueIds);

                    Log::info('Created new variant', [
                        'new_variant_id' => $newVariant->id ?? 'unknown',
                        'attribute_value_ids' => $attributeValueIds
                    ]);

                    // Verify that the variant was created with attribute values
                    if ($newVariant) {
                        $savedAttributeValueIds = $newVariant->attributeValues()->pluck('product_attribute_values.id')->toArray();
                        Log::info('New variant created with attribute values', [
                            'variant_id' => $newVariant->id,
                            'saved_attribute_value_ids' => $savedAttributeValueIds
                        ]);

                        // If no attribute values were saved, try to attach them directly
                        if (empty($savedAttributeValueIds) && !empty($attributeValueIds)) {
                            Log::warning('No attribute values were saved for new variant, attempting to attach directly', [
                                'variant_id' => $newVariant->id,
                                'attribute_value_ids' => $attributeValueIds
                            ]);

                            try {
                                $newVariant->attributeValues()->attach($attributeValueIds);
                                Log::info('Attribute values attached directly to new variant', [
                                    'variant_id' => $newVariant->id,
                                    'attribute_value_ids' => $attributeValueIds
                                ]);
                            } catch (\Exception $e) {
                                Log::error('Failed to attach attribute values directly to new variant', [
                                    'variant_id' => $newVariant->id,
                                    'error' => $e->getMessage()
                                ]);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Handle wholesale prices creation
     *
     * @param int $productId
     * @param array $validated
     * @return void
     */
    private function handleWholesalePrices(int $productId, array $validated): void
    {
        if (isset($validated['wholesale_prices']) && is_array($validated['wholesale_prices']) && !empty($validated['wholesale_prices'])) {
            foreach ($validated['wholesale_prices'] as $wholesaleData) {
                $this->productService->addWholesalePrice($productId, [
                    'min_quantity' => $wholesaleData['min_quantity'],
                    'price' => $wholesaleData['price']
                ]);
            }
        }
    }

    /**
     * Handle wholesale prices update
     *
     * @param object $product
     * @param array $validated
     * @return void
     */
    private function handleWholesalePricesUpdate(object $product, array $validated): void
    {
        $hasWholesale = false;

        if (isset($validated['wholesale_prices']) && is_array($validated['wholesale_prices']) && !empty($validated['wholesale_prices'])) {
            // If there are wholesale prices, ensure has_wholesale is set to 1
            $hasWholesale = true;

            // Get existing wholesale prices
            $existingPriceIds = collect($validated['wholesale_prices'])
                ->filter(function ($price) {
                    return isset($price['id']) && $price['id'] > 0;
                })
                ->pluck('id')
                ->toArray();

            // Delete prices not in the request
            foreach ($product->wholesalePrices as $price) {
                if (!in_array($price->id, $existingPriceIds)) {
                    $this->productService->deleteWholesalePrice($price->id);
                }
            }

            // Update or create wholesale prices
            foreach ($validated['wholesale_prices'] as $priceData) {
                if (isset($priceData['id']) && $priceData['id'] > 0) {
                    // Update existing price
                    $this->productService->updateWholesalePrice($priceData['id'], [
                        'min_quantity' => $priceData['min_quantity'],
                        'price' => $priceData['price']
                    ]);
                } else if (isset($priceData['min_quantity']) && isset($priceData['price'])) {
                    // Create new price
                    $this->productService->addWholesalePrice($product->id, [
                        'min_quantity' => $priceData['min_quantity'],
                        'price' => $priceData['price']
                    ]);
                }
            }
        } else if (isset($validated['has_wholesale']) && $validated['has_wholesale'] == 0) {
            // If wholesale is disabled, delete all wholesale prices
            foreach ($product->wholesalePrices as $price) {
                $this->productService->deleteWholesalePrice($price->id);
            }
        }

        // Update the product with the correct has_wholesale value if needed
        if ($hasWholesale) {
            $this->productService->update($product->id, ['has_wholesale' => 1]);
        }
    }

    /**
     * Handle shipping origins creation
     *
     * @param int $productId
     * @param array $validated
     * @return void
     */
    private function handleShippingOrigins(int $productId, array $validated): void
    {
        if (isset($validated['address_selection_type']) && $validated['address_selection_type'] === 'saved') {
            // Check if selected_address_id exists and is not empty
            if (isset($validated['selected_address_id']) && !empty($validated['selected_address_id'])) {
                $this->productService->addShippingOrigin($productId, [
                    'shipping_address_id' => $validated['selected_address_id']
                ]);
            } else {
                // Log warning if no address is selected
                \Log::warning('No shipping address selected for product', ['product_id' => $productId]);
            }
        } elseif (isset($validated['address_selection_type']) && $validated['address_selection_type'] === 'new' && isset($validated['shipping_origins'])) {
            foreach ($validated['shipping_origins'] as $originData) {
                try {
                    // Log data yang diterima untuk debugging
                    \Log::info('Received shipping origin data', [
                        'product_id' => $productId,
                        'data' => $originData
                    ]);

                    // Create a new shipping address first
                    $isDefault = isset($originData['save_as_default']) ? (bool) $originData['save_as_default'] : false;

                    // Jika ini adalah alamat default, reset semua alamat lain
                    if ($isDefault) {
                        ShippingAddress::where('user_id', auth()->id())
                            ->update(['is_default' => false]);
                    }

                    // Buat alamat baru menggunakan create untuk menghindari masalah dengan nama kolom
                    $shippingAddress = ShippingAddress::create([
                        'user_id' => auth()->id(),
                        'name' => $originData['name'] ?? 'Alamat Pengiriman',
                        'address' => $originData['address'] ?? '',
                        'city' => $originData['city'] ?? '',
                        'state' => $originData['state'] ?? '',
                        'country' => $originData['country'] ?? 'Indonesia',
                        'postal_code' => $originData['postal_code'] ?? '',
                        'is_default' => $isDefault
                    ]);

                    // Log alamat yang dibuat
                    \Log::info('Created new shipping address', [
                        'address_id' => $shippingAddress->id,
                        'name' => $shippingAddress->name,
                        'address' => $shippingAddress->address
                    ]);

                    // Now add the shipping origin with the new address ID
                    $origin = new ProductShippingOrigin();
                    $origin->product_id = $productId;
                    $origin->shipping_address_id = $shippingAddress->id;
                    $origin->is_default = $shippingAddress->is_default;
                    $origin->save();

                    \Log::info('Added new shipping origin for product', [
                        'product_id' => $productId,
                        'origin_id' => $origin->id,
                        'address_id' => $shippingAddress->id
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to create shipping address and origin', [
                        'product_id' => $productId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'data' => $originData
                    ]);
                }
            }
        }
    }

    /**
     * Handle shipping origins update
     *
     * @param object $product
     * @param array $validated
     * @return void
     */
    private function handleShippingOriginsUpdate(object $product, array $validated): void
    {
        // Handle based on address selection type
        if (isset($validated['address_selection_type']) && $validated['address_selection_type'] === 'saved') {
            // Check if selected_address_id exists and is not empty
            if (isset($validated['selected_address_id']) && !empty($validated['selected_address_id'])) {
                // Delete existing origins first
                foreach ($product->shippingOrigins as $origin) {
                    $this->productService->deleteShippingOrigin($origin->id);
                }

                // Add the selected saved address as the shipping origin
                $this->productService->addShippingOrigin($product->id, [
                    'shipping_address_id' => $validated['selected_address_id']
                ]);
            } else {
                // Log warning if no address is selected
                \Log::warning('No shipping address selected for product update', ['product_id' => $product->id]);
            }
        } elseif (isset($validated['shipping_origins'])) {
            // Get existing shipping origins
            $existingOriginIds = collect($validated['shipping_origins'])
                ->filter(function ($origin) {
                    return isset($origin['id']) && $origin['id'] > 0;
                })
                ->pluck('id')
                ->toArray();

            // Delete origins not in the request
            foreach ($product->shippingOrigins as $origin) {
                if (!in_array($origin->id, $existingOriginIds)) {
                    $this->productService->deleteShippingOrigin($origin->id);
                }
            }

            // Update or create shipping origins
            foreach ($validated['shipping_origins'] as $originData) {
                if (isset($originData['id']) && $originData['id'] > 0) {
                    $this->productService->updateShippingOrigin($originData['id'], $originData);
                } else {
                    $this->productService->addShippingOrigin($product->id, $originData);
                }
            }
        }
    }

    /**
     * Handle responsible users creation
     *
     * @param int $productId
     * @param array $validated
     * @return void
     */
    // private function handleResponsibleUsers(int $productId, array $validated): void
    // {
    //     foreach ($validated['responsible_users'] as $userId) {
    //         $this->productService->addResponsibleUser($productId, [
    //             'user_id' => $userId,
    //             'distribution_type' => 'equal',
    //             'distribution_value' => null
    //         ]);
    //     }
    // }
    private function handleResponsibleUsers(int $productId, array $validated): void
    {
        // First, delete all existing responsible users for this product
        // $this->productService->deleteAllResponsibleUsers($productId);

        $distributionType = $validated['distribution_type'];
        $distributionValues = $validated['distribution_values'] ?? [];

        // Validate distribution values based on type
        if ($distributionType === 'percentage') {
            $totalPercentage = array_sum($distributionValues);
            if (abs($totalPercentage - 100) > 0.01) {
                throw new \InvalidArgumentException('Total persentase distribusi harus tepat 100%');
            }
        }

        foreach ($validated['responsible_users'] as $userId) {
            $distributionValue = null;

            if ($distributionType !== 'equal') {
                if (!isset($distributionValues[$userId])) {
                    throw new \InvalidArgumentException("Nilai distribusi tidak ditemukan untuk user ID: {$userId}");
                }

                $distributionValue = $distributionValues[$userId];

                // Additional validation
                if ($distributionType === 'percentage' && ($distributionValue <= 0 || $distributionValue > 100)) {
                    throw new \InvalidArgumentException("Persentase distribusi harus antara 0-100% untuk user ID: {$userId}");
                }

                if ($distributionType === 'fixed' && $distributionValue < 1) {
                    throw new \InvalidArgumentException("Nilai distribusi tetap harus minimal 1 untuk user ID: {$userId}");
                }
            }

            $this->productService->addResponsibleUser($productId, [
                'user_id' => $userId,
                'distribution_type' => $distributionType,
                'distribution_value' => $distributionValue
            ]);
        }
    }

    /**
     * Validate price based on product type
     *
     * @param array $validated
     * @return void
     * @throws \Exception
     */
    private function validatePriceBasedOnType(array $validated): void
    {
        // Check if product has variants
        if (isset($validated['has_variants']) && $validated['has_variants']) {
            // If product has variants, check if variants array exists and has items
            if (!isset($validated['variants']) || empty($validated['variants'])) {
                throw new \Exception('Produk dengan variasi harus memiliki minimal satu variasi dengan harga.');
            }

            // Check if all variants have prices
            foreach ($validated['variants'] as $variant) {
                if (!isset($variant['price']) || empty($variant['price'])) {
                    throw new \Exception('Semua variasi produk harus memiliki harga.');
                }
            }
        } else {
            // If product doesn't have variants, check if regular price is set
            if (!isset($validated['regular_price']) || empty($validated['regular_price'])) {
                throw new \Exception('Harga normal wajib diisi untuk produk tanpa variasi.');
            }
        }
    }

    /**
     * Handle responsible users update
     *
     * @param object $product
     * @param array $validated
     * @return void
     */
    private function handleResponsibleUsersUpdate(object $product, array $validated): void
    {
        if (isset($validated['responsible_users'])) {
            Log::info('Updating responsible users', [
                'product_id' => $product->id,
                'responsible_users' => $validated['responsible_users'],
                'distribution_type' => $validated['distribution_type'] ?? 'equal',
                'distribution_values' => $validated['distribution_values'] ?? []
            ]);

            // First, delete all existing responsible users for this product
            foreach ($product->responsibleUsers as $existingUser) {
                $this->productService->deleteResponsibleUser($existingUser->id);
            }

            // Get distribution data
            $distributionType = $validated['distribution_type'] ?? 'equal';
            $distributionValues = $validated['distribution_values'] ?? [];

            // Validate distribution values based on type
            if ($distributionType === 'percentage') {
                $totalPercentage = array_sum($distributionValues);
                if (abs($totalPercentage - 100) > 0.01) {
                    throw new \InvalidArgumentException('Total persentase distribusi harus tepat 100%');
                }
            }

            // Create new responsible users
            foreach ($validated['responsible_users'] as $userId) {
                $distributionValue = null;

                if ($distributionType !== 'equal') {
                    if (!isset($distributionValues[$userId])) {
                        throw new \InvalidArgumentException("Nilai distribusi tidak ditemukan untuk user ID: {$userId}");
                    }

                    $distributionValue = $distributionValues[$userId];

                    // Additional validation
                    if ($distributionType === 'percentage' && ($distributionValue <= 0 || $distributionValue > 100)) {
                        throw new \InvalidArgumentException("Persentase distribusi harus antara 0-100% untuk user ID: {$userId}");
                    }

                    if ($distributionType === 'fixed' && $distributionValue < 1) {
                        throw new \InvalidArgumentException("Nilai distribusi tetap harus minimal 1 untuk user ID: {$userId}");
                    }
                }

                $this->productService->addResponsibleUser($product->id, [
                    'user_id' => $userId,
                    'distribution_type' => $distributionType,
                    'distribution_value' => $distributionValue
                ]);

                Log::info('Added responsible user', [
                    'product_id' => $product->id,
                    'user_id' => $userId,
                    'distribution_type' => $distributionType,
                    'distribution_value' => $distributionValue
                ]);
            }
        }
    }
}

