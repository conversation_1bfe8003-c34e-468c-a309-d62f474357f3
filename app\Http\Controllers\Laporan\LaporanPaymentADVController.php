<?php

namespace App\Http\Controllers\Laporan;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Laporan\PaymentAdvService;

class LaporanPaymentADVController extends Controller
{
    public function __construct(protected PaymentAdvService $paymentAdvService)
    {
    }

    public function index()
    {
        return view('laporan.payment-adv.index', [
            'title' => 'Laporan Payment ADV',
            'advertisers' => User::where('level_user', 4)->orderBy('name', 'asc')->get(),
        ]);
    }

    public function data(Request $request)
    {
        $filter = [];

        if ($request->has('adv_id')) {
            $filter['adv_id'] = $request->get('adv_id');
        }

        if ($request->has('bulan')) {
            $bulanSearch = $request->get('bulan');
        }

        $data = $this->paymentAdvService->summary($filter['adv_id'] ?? null, $bulanSearch ?? null);

        return response()->json($data);
    }
}
