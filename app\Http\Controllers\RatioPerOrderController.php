<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMaster\PegawaiRequest;
use App\Services\Ratio\RatioPerOrderService;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class RatioPerOrderController extends Controller
{
    public function __construct(protected RatioPerOrderService $ratioPerOrderService)
    {
    }

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('ratio.order.index', [
            'title' => 'Ratio Close Per Order'
        ]);
    }

    public function data()
    {
        if (request()->has('date')) {
            $dateRange = request()->get('date');
        }
        $data = $this->ratioPerOrderService->grid($dateRange);
        return DataTables::of($data)
            ->addIndexColumn()
            // ->addColumn('action', function ($user) {
            //     return '
            //         <span class="text-info cursor-pointer btn-edit" data-id="' . $user->id . '">
            //             <i class="bx bx-message-square-edit icon-md"></i>
            //         </span>
            //         <span class="text-danger cursor-pointer btn-delete" data-id="' . $user->id . '">
            //             <i class="bx bx-trash icon-md"></i>
            //         </span>
            //     ';
            // })
            // ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'levelUser' => LevelUser::toArray(),
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PegawaiRequest $request)
    {
        $this->ratioPerOrderService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = $this->ratioPerOrderService->show($id);
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'pegawai' => $user,
            'levelUser' => LevelUser::toArray(),
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PegawaiRequest $request, string $id)
    {
        $this->ratioPerOrderService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->ratioPerOrderService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }
}
