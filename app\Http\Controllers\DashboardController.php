<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
// use App\Http\Libraries\Activitys;

class DashboardController extends Controller
{
    protected $data = [];

    public function __construct()
    {
        // $this->middleware('guest')->except('logout');
    }

    public function index(Request $request)
    {
        $this->data['title'] = 'Dashboard';
        $this->data['pendapatan'] = Order::whereNotNull('created_at')
            ->where(function ($query) {
            $query->where(function ($subQuery) {
                $subQuery->where('payment_method', 'bank_transfer')
                    ->where('payment_status', 'paid')
                    ->where('status', 'completed');
            })->orWhere(function ($subQuery) {
                $subQuery->where('payment_method', 'cod')
                    ->where('payment_status', 'unpaid')
                    ->where('status', 'completed');
            });
            })
            ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->sum('net_revenue');
        $this->data['pendapatan_minggu_lalu'] = Order::whereNotNull('created_at')
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('payment_method', 'bank_transfer')
                        ->where('payment_status', 'paid')
                        ->where('status', 'completed');
                })->orWhere(function ($subQuery) {
                    $subQuery->where('payment_method', 'cod')
                        ->where('payment_status', 'unpaid')
                        ->where('status', 'completed');
                });
            })
            ->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()])
            ->sum('net_revenue');
        $this->data['order'] = Order::where('status', 'completed')
            ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->count();
        $this->data['order_minggu_lalu'] = Order::where('status', 'completed')
            ->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()])
            ->count();
        $this->data['total_pengiriman'] = Order::whereNotNull('receipt_number')
            ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->count();
        $this->data['total_pengiriman_minggu_lalu'] = Order::whereNotNull('receipt_number')
            ->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()])
            ->count();
        $this->data['total_retur'] = Order::where('status', 'retur')
            ->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
            ->count();
        $this->data['total_retur_minggu_lalu'] = Order::where('status', 'retur')
            ->whereBetween('created_at', [now()->subWeek()->startOfWeek(), now()->subWeek()->endOfWeek()])
            ->count();
        return view('dashboard')->with('data', $this->data);
    }

    public function getDataBarChart(Request $request){
        $data = Order::selectRaw('MONTH(created_at) as month, COUNT(*) as close_order')
            ->whereYear('created_at', now()->year)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                    $subQuery->where('payment_method', 'bank_transfer')
                    ->where('payment_status', 'paid')
                    ->where('status', 'completed');
                })->orWhere(function ($subQuery) {
                    $subQuery->where('payment_method', 'cod')
                    ->where('payment_status', 'unpaid')
                    ->where('status', 'completed');
                });
            })
            ->groupByRaw('MONTH(created_at)')
            ->orderByRaw('MONTH(created_at)')
            ->get();

        $leadData = Order::selectRaw('MONTH(created_at) as month, COUNT(*) as lead_order')
            ->whereYear('created_at', now()->year)
            ->where('status', '!=', 'cancelled')
            ->groupByRaw('MONTH(created_at)')
            ->orderByRaw('MONTH(created_at)')
            ->get();

        $bankTransferData = Order::selectRaw('MONTH(created_at) as month, COUNT(*) as bank_transfer_order')
            ->whereYear('created_at', now()->year)
            ->where('payment_method', 'bank_transfer')
            ->where('payment_status', 'paid')
            ->where('status', 'completed')
            ->groupByRaw('MONTH(created_at)')
            ->orderByRaw('MONTH(created_at)')
            ->get();

        $codData = Order::selectRaw('MONTH(created_at) as month, COUNT(*) as cod_order')
            ->whereYear('created_at', now()->year)
            ->where('payment_method', 'cod')
            ->where('payment_status', 'unpaid')
            ->where('status', 'completed')
            ->groupByRaw('MONTH(created_at)')
            ->orderByRaw('MONTH(created_at)')
            ->get();

        $labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $close_order = array_fill(0, 12, 0);
        $lead_order = array_fill(0, 12, 0);

        foreach ($data as $item) {
            $close_order[$item->month - 1] = $item->close_order;
        }

        foreach ($leadData as $item) {
            $lead_order[$item->month - 1] = $item->lead_order;
        }

        $total_order = array_sum($close_order);

        $total_transfer = array_sum(array_column($bankTransferData->toArray(), 'bank_transfer_order'));
        $total_cod = array_sum(array_column($codData->toArray(), 'cod_order'));

        $total_order = array_sum(array_column($data->toArray(), 'close_order'));
        $total_transfer = Order::whereYear('created_at', now()->year)
            ->where('payment_method', 'bank_transfer')
            ->where('payment_status', 'paid')
            ->where('status', 'completed')
            ->sum('net_revenue');
        $total_cod = Order::whereYear('created_at', now()->year)
            ->where('payment_method', 'cod')
            ->where('payment_status', 'unpaid')
            ->where('status', 'completed')
            ->sum('net_revenue');

        return response()->json([
            'labels' => array_slice($labels, 0, now()->month),
            'close_order' => array_slice($close_order, 0, now()->month),
            'lead_order' => array_slice($lead_order, 0, now()->month),
            'total_order' => $total_order,
            'total_transfer' => $total_transfer,
            'total_cod' => $total_cod,
            'total_revenue' => Order::whereYear('created_at', now()->year)
            ->where(function ($query) {
                $query->where(function ($subQuery) {
                $subQuery->where('payment_method', 'bank_transfer')
                    ->where('payment_status', 'paid')
                    ->where('status', 'completed');
                })->orWhere(function ($subQuery) {
                $subQuery->where('payment_method', 'cod')
                    ->where('payment_status', 'unpaid')
                    ->where('status', 'completed');
                });
            })
            ->sum('net_revenue'),
        ]);
    }

    public function getDataPieChart(Request $request){
        $data = Order::selectRaw('payment_method, COUNT(*) as count')
            ->where('status', 'completed')
            ->groupBy('payment_method')
            ->get();

        $labels = [];
        $counts = [];

        foreach ($data as $item) {
            $labels[] = $item->payment_method;
            $counts[] = $item->count;
        }

        return response()->json([
            'labels' => $labels,
            'counts' => $counts,
        ]);
    }
}
