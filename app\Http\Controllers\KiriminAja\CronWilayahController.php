<?php

namespace App\Http\Controllers\KiriminAja;

use App\Http\Controllers\Controller;
use App\Services\KiriminAja\Area\KiriminAjaAreaService;
use Illuminate\Http\Request;
use App\Models\Province;
use App\Models\City;
use App\Models\District;
use App\Models\SubDistrict;
use App\Jobs\GetProvinceKiriminAjaJob;
use App\Jobs\GetCityKiriminAjaJob;
use App\Jobs\GetDistrictKiriminAjaJob;
use Illuminate\Support\Facades\Artisan;

class CronWilayahController extends Controller
{
    public function __construct(protected KiriminAjaAreaService $kiriminAjaAreaService) {}

    public function getProvinces()
    {
        try {
            // Start the queue worker
            \Artisan::call('queue:work', [
                '--stop-when-empty' => true
            ]);

            GetProvinceKiriminAjaJob::dispatch();
            return response()->json(['status' => true, 'message' => 'Provinces synchronized successfully.']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Failed to synchronize provinces: ' . $e->getMessage()], 500);
        }
    }

    public function syncCities()
    {
        try {

            GetCityKiriminAjaJob::dispatch();
            return response()->json(['status' => true, 'message' => 'Cities synchronized successfully.']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Failed to synchronize Cities: ' . $e->getMessage()], 500);
        }
    }
    public function syncDistricts()
    {
        try {
            $cities = City::get();
            $counter = 0;

            foreach ($cities as $city) {
                GetDistrictKiriminAjaJob::dispatch($city->id);
                $counter++;

                // Pause for 2 minutes after every 10 cities
                if ($counter % 10 === 0) {
                    sleep(120); // 120 seconds = 2 minutes
                }
            }

            return response()->json(['status' => true, 'message' => 'Districts synchronized successfully.']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => 'Failed to synchronize Districts: ' . $e->getMessage()], 500);
        }
    }
}
