<?php

namespace App\Http\Controllers;

use App\Http\Requests\LandingPageRequest;
use App\Models\LandingPage;
use App\Models\Product;
use App\Services\LandingPageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Yajra\DataTables\Facades\DataTables;

class LandingPageController extends Controller
{
    protected $landingPageService;

    public function __construct(LandingPageService $landingPageService)
    {
        $this->landingPageService = $landingPageService;
    }

    public function index()
    {
        return view('landing-page.index', ['title' => 'Landing Page']);
    }

    public function data()
    {
        // Optimized query - only load necessary fields and relations
        $landingPages = LandingPage::select([
            'id',
            'name',
            'slug',
            'user_id',
            'is_active',
            'visitors',
            'created_at'
        ])
            ->with([
                'product:id,name', // Only load necessary product fields
                'user:id,name'     // Only load necessary user fields
            ])
            ->byUser(Auth::id())
            ->active()
            ->latest();

        return DataTables::of($landingPages)
            ->addIndexColumn()
            ->addColumn('product_name', function ($row) {
                return $row->product ? $row->product->name : '-';
            })
            ->addColumn('action', function ($row) {
                return view('landing-page.partials.action-buttons', compact('row'))->render();
            })
            ->addColumn('status', function ($row) {
                $badgeClass = $row->is_active ? 'bg-success' : 'bg-secondary';
                $status = $row->is_active ? 'Active' : 'Inactive';
                return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
            })
            ->rawColumns(['action', 'status'])
            ->make(true);
    }

    public function getProducts()
    {
        try {
            // Cache products for 1 hour to improve performance
            $products = Cache::remember('active_products_for_landing', 3600, function () {
                return Product::select('id', 'name', 'regular_price', 'discount_price')
                    ->orderBy('name')
                    ->active()
                    ->get();
            });

            return response()->json([
                'success' => true,
                'data' => $products
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat data produk: ' . $e->getMessage()
            ], 500);
        }
    }

    public function create()
    {
        $products = Product::select('id', 'name', 'regular_price', 'discount_price')
            ->orderBy('name')
            ->active()
            ->get();

        return view('landing-page.create', [
            'title' => 'Buat Landing Page',
            'products' => $products
        ]);
    }

    public function store(LandingPageRequest $request)
    {
        $result = $this->landingPageService->createLandingPage($request, Auth::id());

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    public function edit($id)
    {
        $landingPage = LandingPage::with([
            'product',
            'section2Product',
            'section3Product',
            'section4Product',
            'section5Product',
            'section6.product'
        ])->findOrFail($id);

        // Check if user owns this landing page
        if ($landingPage->user_id !== Auth::id()) {
            abort(403);
        }

        $products = Product::select('id', 'name', 'regular_price', 'discount_price')
            ->orderBy('name')
            ->active()
            ->get();

        return view('landing-page.edit', [
            'title' => 'Edit Landing Page',
            'landingPage' => $landingPage,
            'products' => $products
        ]);
    }

    public function update(LandingPageRequest $request, $id)
    {
        $landingPage = LandingPage::findOrFail($id);

        // Check if user owns this landing page
        if ($landingPage->user_id !== Auth::id()) {
            abort(403);
        }

        $result = $this->landingPageService->updateLandingPage($request, $landingPage);

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    public function destroy($id)
    {
        $landingPage = LandingPage::findOrFail($id);

        // Check if user owns this landing page
        if ($landingPage->user_id !== Auth::id()) {
            abort(403);
        }

        $result = $this->landingPageService->deleteLandingPage($landingPage);

        return response()->json($result, $result['success'] ? 200 : 500);
    }

    public function preview($slug)
    {
        $landingPage = LandingPage::with([
            'product',
            'section2Product',
            'section3Product',
            'section4Product',
            'section5Product',
            'section6.product'
        ])->where('slug', $slug)->firstOrFail();

        return view('landing-page.preview', compact('landingPage'));
    }

    public function show($slug)
    {
        // Load all necessary data for complete rendering
        $landingPage = LandingPage::with([
            'product',
            'section2Product',
            'section3Product',
            'section4Product',
            'section5Product',
            'section6.product',
            'facebookPixel'
        ])
            ->where('slug', $slug)
            ->active()
            ->firstOrFail();

        // Increment visitor count
        $landingPage->incrementVisitors();

        return view('landing-page.show', compact('landingPage'));
    }
}
