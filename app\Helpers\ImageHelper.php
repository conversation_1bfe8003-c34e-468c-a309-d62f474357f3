<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    /**
     * Get a flexible image URL that works in both local and production environments
     * 
     * @param string|null $imagePath
     * @param string|null $fallback
     * @return string
     */
    public static function getImageUrl(?string $imagePath, ?string $fallback = null): string
    {
        if (empty($imagePath)) {
            return $fallback ?? '';
        }

        // Handle different image path formats for better deployment compatibility
        if (str_starts_with($imagePath, 'http')) {
            // Already a full URL (external image or CDN)
            return $imagePath;
        }
        
        if (str_starts_with($imagePath, 'storage/')) {
            // Already has storage prefix (legacy format)
            return asset($imagePath);
        }
        
        if (str_starts_with($imagePath, 'landing-pages/') || 
            str_starts_with($imagePath, 'products/') || 
            str_starts_with($imagePath, 'uploads/')) {
            // Relative path from storage/app/public
            return asset("storage/{$imagePath}");
        }
        
        // Fallback to Storage::url() for other cases
        try {
            return Storage::url($imagePath);
        } catch (\Exception $e) {
            // If Storage::url() fails, try asset() as last resort
            return asset("storage/{$imagePath}");
        }
    }

    /**
     * Get image URL with additional checks for file existence
     * 
     * @param string|null $imagePath
     * @param string|null $fallback
     * @return string
     */
    public static function getImageUrlWithCheck(?string $imagePath, ?string $fallback = null): string
    {
        if (empty($imagePath)) {
            return $fallback ?? '';
        }

        // For external URLs, return as-is
        if (str_starts_with($imagePath, 'http')) {
            return $imagePath;
        }

        // Check if file exists in storage
        $diskPath = str_starts_with($imagePath, 'storage/') 
            ? str_replace('storage/', '', $imagePath)
            : $imagePath;

        if (Storage::disk('public')->exists($diskPath)) {
            return self::getImageUrl($imagePath, $fallback);
        }

        // File doesn't exist, return fallback
        return $fallback ?? '';
    }

    /**
     * Get optimized image URL for different sizes
     * 
     * @param string|null $imagePath
     * @param string $size (thumbnail, medium, large)
     * @param string|null $fallback
     * @return string
     */
    public static function getOptimizedImageUrl(?string $imagePath, string $size = 'medium', ?string $fallback = null): string
    {
        if (empty($imagePath)) {
            return $fallback ?? '';
        }

        // For now, return the original image
        // In the future, this could be extended to serve different sizes
        return self::getImageUrl($imagePath, $fallback);
    }
}
