<?php

namespace App\Http\Controllers;

use App\Models\FacebookPixel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FacebookPixelController extends Controller
{
    /**
     * Get Facebook pixels for the current user.
     */
    public function getPixels()
    {
        try {
            $pixels = FacebookPixel::select('id', 'name', 'pixel_id')
                ->byUser(Auth::id())
                ->active()
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $pixels
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memuat data pixel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a new Facebook pixel.
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'pixel_id' => 'required|string|size:15|regex:/^\d{15}$/|unique:facebook_pixels,pixel_id',
                'description' => 'nullable|string|max:500'
            ], [
                'name.required' => 'Nama pixel harus diisi.',
                'pixel_id.required' => 'Pixel ID harus diisi.',
                'pixel_id.size' => 'Pixel ID harus berupa 15 digit angka.',
                'pixel_id.regex' => 'Pixel ID harus berupa 15 digit angka.',
                'pixel_id.unique' => 'Pixel ID sudah digunakan.',
                'description.max' => 'Deskripsi maksimal 500 karakter.'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal.',
                    'errors' => $validator->errors()
                ], 422);
            }

            $pixel = FacebookPixel::create([
                'user_id' => Auth::id(),
                'name' => $request->name,
                'pixel_id' => $request->pixel_id,
                'description' => $request->description,
                'is_active' => true
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Facebook Pixel berhasil disimpan.',
                'data' => [
                    'id' => $pixel->id,
                    'name' => $pixel->name,
                    'pixel_id' => $pixel->pixel_id
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan pixel: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * FUNGSI EVENTS DAN PARAMETERS DIHAPUS
     * Implementasi pixel sekarang menggunakan sistem manual sederhana
     * Hanya melacak 2 event: ViewContent & Purchase
     * Data diambil langsung dari produk, tidak perlu konfigurasi rumit
     */
}
