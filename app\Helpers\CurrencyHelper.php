<?php

namespace App\Helpers;

/**
 * Helper class for currency formatting and parsing
 */
class CurrencyHelper
{
    /**
     * Parse Indonesian formatted number to decimal
     *
     * @param string|null $formattedNumber
     * @return float
     */
    public static function parseIndonesianNumber($formattedNumber)
    {
        if (empty($formattedNumber)) {
            return 0;
        }

        // Remove all dots (thousand separators) and replace comma with dot
        $cleanValue = str_replace('.', '', $formattedNumber);
        $cleanValue = str_replace(',', '.', $cleanValue);

        // Convert to float
        return (float) $cleanValue;
    }

    /**
     * Parse database formatted number to decimal
     * This handles the case where the database stores values with comma as thousand separator
     *
     * @param string|null $dbFormattedNumber
     * @return float
     */
    public static function parseDatabaseNumber($dbFormattedNumber)
    {
        if (empty($dbFormattedNumber)) {
            return 0;
        }

        // If the value contains a comma, it's likely using comma as thousand separator
        if (strpos($dbFormattedNumber, ',') !== false) {
            // Remove all commas (thousand separators)
            $cleanValue = str_replace(',', '', $dbFormattedNumber);

            // Convert to float
            return (float) $cleanValue;
        }

        // If no comma, just convert to float
        return (float) $dbFormattedNumber;
    }

    /**
     * Format number to Indonesian format
     *
     * @param float|null $number
     * @param int $precision
     * @return string
     */
    public static function formatToIndonesian($number, $precision = 0)
    {
        if (empty($number) || !is_numeric($number)) {
            $number = 0;
        }

        // Format with specified precision
        return number_format($number, $precision, ',', '.');
    }

    /**
     * Format number as Indonesian Rupiah
     *
     * @param float|null $number
     * @param int $precision
     * @return string
     */
    public static function formatRupiah($number, $precision = 0)
    {
        return 'Rp ' . self::formatToIndonesian($number, $precision);
    }

    /**
     * Format number to database format
     * This formats a number to match the format stored in the database (with comma as thousand separator)
     *
     * @param float|null $number
     * @param int $precision
     * @return string
     */
    public static function formatToDatabase($number, $precision = 1)
    {
        if (empty($number) || !is_numeric($number)) {
            $number = 0;
        }

        // Format with specified precision and comma as thousand separator
        return number_format($number, $precision, '.', ',');
    }

    /**
     * Clean currency input from non-numeric characters
     *
     * @param string|null $input
     * @return float
     */
    public static function cleanCurrencyInput($input)
    {
        if (empty($input)) {
            return 0;
        }

        // Remove currency symbol, spaces, and other non-numeric characters except decimal separator
        $cleaned = preg_replace('/[^\d,\.]/', '', $input);

        // Check if the input uses Indonesian format (with comma as decimal separator)
        if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
            // Indonesian format with both thousand and decimal separators
            return self::parseIndonesianNumber($cleaned);
        } else if (strpos($cleaned, ',') !== false) {
            // Indonesian format with only decimal separator
            return (float) str_replace(',', '.', $cleaned);
        } else {
            // Standard format or only with thousand separator
            return (float) str_replace('.', '', $cleaned);
        }
    }

    /**
     * Validate currency value
     *
     * @param float $value
     * @param float $min
     * @param float $max
     * @return bool
     */
    public static function validateCurrencyValue($value, $min = 0, $max = 1000000000)
    {
        return $value >= $min && $value <= $max;
    }
}
