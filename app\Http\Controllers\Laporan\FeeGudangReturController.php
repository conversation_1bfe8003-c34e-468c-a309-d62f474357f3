<?php

namespace App\Http\Controllers\Laporan;

use App\Enums\LevelUser;
use App\Http\Controllers\Controller;
use App\Http\Requests\DataMaster\PegawaiRequest;
use App\Services\Laporan\FeeGudangService;
use Yajra\DataTables\Facades\DataTables;
use App\Models\User;
use Illuminate\Http\Request;

class FeeGudangReturController extends Controller
{
    public function __construct(protected FeeGudangService $FeeGudangService){}

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('laporan.fee-gudang-retur.index', [
            'title' => 'Laporan Fee Gudang Retur',
        ]);
    }

    public function data(Request $request)
    {                
        $tanggal_awal = $request->input('tanggal_awal');
        $tanggal_akhir = $request->input('tanggal_akhir');
        $jenis = 'retur';

        $data = $this->FeeGudangService->grid($tanggal_awal, $tanggal_akhir, $jenis);

        if (empty($data)) {
            return DataTables::of(collect([]))->make(true);
        }
        
        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('fee_packing', function ($data) {        
                return ($data->gross_revenue >= 130000) ? '6000' : '4000';
            })
            ->addColumn('fee_gudang', function ($data) {                
                return isset($data->harga_jual) ? (($data->harga_jual * 5 / 100) + $data->total_biaya_kulak) : '0';
            })                                    
            ->rawColumns(['fee_packing','fee_gudang'])
            ->make(true);
    }
}
