<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ProductAttribute;
use App\Models\ProductAttributeValue;
use Illuminate\Http\Request;

class ProductAttributeController extends Controller
{
    /**
     * Get all attribute values with their IDs
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAttributeValues()
    {
        try {
            // Get all attributes with their values
            $attributes = ProductAttribute::with('values')->get();
            
            // Format the data as a nested array
            $result = [];
            foreach ($attributes as $attribute) {
                $result[$attribute->name] = [];
                foreach ($attribute->values as $value) {
                    $result[$attribute->name][$value->value] = $value->id;
                }
            }
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get attribute values: ' . $e->getMessage()
            ], 500);
        }
    }
}
