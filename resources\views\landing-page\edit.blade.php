@extends('templates.app')

@section('title', $title)

@section('content')
    <h6 class="mb-0 text-uppercase">Edit Landing Page</h6>
    <hr />
    <form id="landingPageForm" action="{{ route('landingPage.update', $landingPage->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <!-- Hidden fields for pixel tracking configuration -->
        <input type="hidden" id="facebook_pixel_id_config" name="facebook_pixel_id" value="{{ $landingPage->facebook_pixel_id }}">
        <input type="hidden" id="pixel_events_config" name="pixel_events" value="{{ is_array($landingPage->pixel_events) ? json_encode($landingPage->pixel_events) : $landingPage->pixel_events }}">
        <input type="hidden" id="pixel_event_parameters_config" name="pixel_event_parameters" value="{{ is_array($landingPage->pixel_event_parameters) ? json_encode($landingPage->pixel_event_parameters) : $landingPage->pixel_event_parameters }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <!-- Left sidebar navigation -->
                    <div class="col-md-3 pe-4">
                        <div class="sidebar-nav-container">
                            <ul class="nav nav-pills flex-column" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section1-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section1" type="button" role="tab">
                                        <span class="section-number me-3">1</span>
                                        <span class="section-title">Section 1</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section2-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section2" type="button" role="tab">
                                        <span class="section-number me-3">2</span>
                                        <span class="section-title">Section 2</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section3-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section3" type="button" role="tab">
                                        <span class="section-number me-3">3</span>
                                        <span class="section-title">Section 3</span>
                                        <span class="badge bg-info ms-auto optional-badge">Optional</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section4-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section4" type="button" role="tab">
                                        <span class="section-number me-3">4</span>
                                        <span class="section-title">Section 4</span>
                                        <span class="badge bg-info ms-auto optional-badge">Optional</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section5-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section5" type="button" role="tab">
                                        <span class="section-number me-3">5</span>
                                        <span class="section-title">Section 5</span>
                                        <span class="badge bg-info ms-auto optional-badge">Optional</span>
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link d-flex align-items-center mb-3 text-start section-nav" id="v-pills-section6-tab" data-bs-toggle="pill" data-bs-target="#v-pills-section6" type="button" role="tab">
                                        <span class="section-number me-3">6</span>
                                        <span class="section-title">Section 6</span>
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Main content area -->
                    <div class="col-md-9 ps-4">
                        <div class="tab-content" id="v-pills-tabContent">
                            <!-- Section 1 Content -->
                            <div class="tab-pane fade show active" id="v-pills-section1" role="tabpanel" aria-labelledby="v-pills-section1-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-home me-2"></i> Landing Page Section 1</h4>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                        <!-- Landing Page Name -->
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Nama Landing Page <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="name" name="name" value="{{ $landingPage->name }}" placeholder="Masukkan nama landing page" required>
                                        </div>

                                        <!-- Product Selection -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="product_id" class="form-label">Pilih Produk <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="product_id" name="product_id" required>
                                                        <option value="">-- Pilih Produk --</option>
                                                        @foreach ($products as $product)
                                                            <option value="{{ $product->id }}"
                                                                    {{ $landingPage->product_id == $product->id ? 'selected' : '' }}
                                                                    data-price="{{ $product->regular_price }}"
                                                                    data-discount="{{ $product->discount_price }}">
                                                                {{ $product->name }} - Rp {{ number_format($product->regular_price, 0, ',', '.') }}
                                                                @if ($product->discount_price)
                                                                    (Diskon: Rp {{ number_format($product->discount_price, 0, ',', '.') }})
                                                                @endif
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Background Color -->
                                                <div class="mb-3">
                                                    <label for="background_color" class="form-label">Background Color <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="color" class="form-control form-control-color" id="background_color" name="background_color" value="{{ $landingPage->background_color ?? '#DCE8FD' }}" title="Choose background color">
                                                        <input type="text" class="form-control" id="background_color_text" value="{{ $landingPage->background_color ?? '#DCE8FD' }}" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Landing Page Title -->
                                        <div class="mb-3">
                                            <label for="main_title" class="form-label">Judul Landing Page <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="main_title" name="main_title" value="{{ $landingPage->main_title }}" placeholder="Masukkan judul landing page" required>
                                        </div>

                                        <!-- Sub Section Title -->
                                        <div class="mb-3">
                                            <label for="sub_title" class="form-label">Sub Judul Section</label>
                                            <input type="text" class="form-control" id="sub_title" name="sub_title" value="{{ $landingPage->sub_title }}" placeholder="Masukkan sub judul section">
                                        </div>

                                        <!-- Image Upload -->
                                        <div class="mb-3">
                                            <label for="section_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                            <input type="file" class="form-control" id="section_image" name="section_image" accept="image/*">
                                            @if ($landingPage->section_image)
                                                <div class="mt-2">
                                                    <img src="{{ Storage::url($landingPage->section_image) }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                                    <div class="form-text">Gambar saat ini</div>
                                                </div>
                                            @endif
                                            <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                            <div id="image_preview" class="mt-2" style="display: none;">
                                                <img id="preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                            </div>
                                        </div>

                                        <!-- Content Description -->
                                        <div class="mb-3">
                                            <label for="content_description" class="form-label">Keterangan</label>
                                            <div id="content_description" style="height: 200px;">{!! $landingPage->content_description !!}</div>
                                            <input type="hidden" id="content_description_hidden" name="content_description" value="{{ $landingPage->content_description }}">
                                            <div class="form-text">Deskripsi konten untuk section ini</div>
                                        </div>

                                        <!-- Pixel Configuration Status -->
                                        <div class="mb-3">
                                            <div class="card border-info">
                                                <div class="card-body p-3">
                                                    <h6 class="card-title mb-2">
                                                        <i class="bx bx-target-lock me-2"></i>Status Facebook Pixel Tracking
                                                    </h6>
                                                    <div id="pixel-status" class="d-flex align-items-center">
                                                        @if ($landingPage->facebook_pixel_id)
                                                            <span class="badge bg-success me-2">
                                                                <i class="bx bx-check me-1"></i>Configured
                                                            </span>
                                                            <small class="text-muted">Pixel tracking sudah dikonfigurasi</small>
                                                        @else
                                                            <span class="badge bg-secondary me-2">
                                                                <i class="bx bx-x me-1"></i>Not Configured
                                                            </span>
                                                            <small class="text-muted">Pixel tracking belum dikonfigurasi</small>
                                                        @endif
                                                    </div>
                                                    <div class="form-text mt-2">
                                                        <small>
                                                            <strong>Info:</strong> Pixel tracking bersifat opsional.
                                                            Anda bisa mulai tanpa pixel, atau upgrade bertahap dari basic ke advanced tracking.
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 2 Content -->
                            <div class="tab-pane fade" id="v-pills-section2" role="tabpanel" aria-labelledby="v-pills-section2-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-list-ul me-2"></i> Landing Page Section 2</h4>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                        <!-- Product Selection and Background Color -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                {{-- <div class="mb-3">
                                                    <label for="section2_product_id" class="form-label">Produk</label>
                                                    <input type="text" class="form-control" id="section2_product_display"
                                                           value="{{ $landingPage->section2Product ? $landingPage->section2Product->name . ' - Rp ' . number_format($landingPage->section2Product->regular_price, 0, ',', '.') : 'Produk akan mengikuti pilihan di Section 1' }}"
                                                           placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                    <input type="hidden" id="section2_product_id" name="section2_product_id" value="{{ $landingPage->section2_product_id }}">
                                                    <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                </div> --}}
                                                <div class="mb-3">
                                                    <label for="section2_product_id" class="form-label">Produk</label>
                                                    <input type="text" class="form-control" id="section2_product_display"
                                                           value="{{ $landingPage->section2Product ? $landingPage->section2Product->name . ' - Rp ' . number_format($landingPage->section2Product->regular_price, 0, ',', '.') : 'Produk utama dari Section 1' }}"
                                                           placeholder="Akan menggunakan produk yang dipilih di Section 1" readonly>
                                                    <input type="hidden" id="section2_product_id" name="section2_product_id" value="{{ $landingPage->section2_product_id }}">
                                                    <div class="form-text">Produk ini akan selalu sama dengan pilihan di Section 1</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Background Color -->
                                                <div class="mb-3">
                                                    <label for="section2_background_color" class="form-label">Background Color <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="color" class="form-control form-control-color" id="section2_background_color" name="section2_background_color" value="{{ $landingPage->section2_background_color ?? '#DCE8FD' }}" title="Choose background color">
                                                        <input type="text" class="form-control" id="section2_background_color_text" value="{{ $landingPage->section2_background_color ?? '#DCE8FD' }}" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Section Title -->
                                        <div class="mb-3">
                                            <label for="section2_title" class="form-label">Judul Section</label>
                                            <input type="text" class="form-control" id="section2_title" name="section2_title" value="{{ $landingPage->section2_title }}" placeholder="Masukkan judul section">
                                        </div>

                                        <!-- Sub Section Title -->
                                        <div class="mb-3">
                                            <label for="section2_sub_title" class="form-label">Sub Judul Section</label>
                                            <input type="text" class="form-control" id="section2_sub_title" name="section2_sub_title" value="{{ $landingPage->section2_sub_title }}" placeholder="Masukkan sub judul section">
                                        </div>

                                        <!-- Image Upload -->
                                        <div class="mb-3">
                                            <label for="section2_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                            <input type="file" class="form-control" id="section2_image" name="section2_image" accept="image/*">
                                            @if ($landingPage->section2_image)
                                                <div class="mt-2">
                                                    <img src="{{ Storage::url($landingPage->section2_image) }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                                    <div class="form-text">Gambar saat ini</div>
                                                </div>
                                            @endif
                                            <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                            <div id="section2_image_preview" class="mt-2" style="display: none;">
                                                <img id="section2_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                            </div>
                                        </div>

                                        <!-- Content Description -->
                                        <div class="mb-3">
                                            <label for="section2_content" class="form-label">Keterangan</label>
                                            <div id="section2_content" style="height: 200px;">{!! $landingPage->section2_content !!}</div>
                                            <input type="hidden" id="section2_content_hidden" name="section2_content" value="{{ $landingPage->section2_content }}">
                                            <div class="form-text">Deskripsi konten untuk section ini</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 3 Content -->
                            <div class="tab-pane fade" id="v-pills-section3" role="tabpanel" aria-labelledby="v-pills-section3-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-cog me-2"></i> Landing Page Section 3 <span class="badge bg-secondary">Optional</span></h4>

                                <div class="card">
                                    <div class="card-body">
                                        <!-- Section 3 Enable Toggle -->
                                        <div class="alert alert-info d-flex align-items-center mb-4">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="section3_enabled" name="section3_enabled"
                                                       {{ $landingPage->section3_enabled ? 'checked' : '' }} onchange="toggleSectionFields(3)">
                                                <label class="form-check-label" for="section3_enabled">
                                                    <i class="bx bx-toggle-left me-1"></i>Aktifkan Section 3
                                                </label>
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="mb-0">Aktifkan untuk menampilkan section 3 di landing page</small>
                                            </div>
                                        </div>

                                        <div id="section3_fields" class="{{ !$landingPage->section3_enabled ? 'disabled-section' : '' }}">
                                            <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                            <!-- Product Selection and Background Color -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="section3_product_id" class="form-label">Produk</label>
                                                        <input type="text" class="form-control" id="section3_product_display"
                                                               value="{{ $landingPage->section3Product ? $landingPage->section3Product->name . ' - Rp ' . number_format($landingPage->section3Product->regular_price, 0, ',', '.') : 'Mengikuti produk utama' }}"
                                                               placeholder="Otomatis menggunakan produk dari Section 1" readonly>
                                                        <input type="hidden" id="section3_product_id" name="section3_product_id" value="{{ $landingPage->section3_product_id }}">
                                                        <div class="form-text">Produk akan disinkronkan dengan pilihan di Section 1</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <!-- Background Color -->
                                                    <div class="mb-3">
                                                        <label for="section3_background_color" class="form-label">Background Color</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="section3_background_color" name="section3_background_color" value="{{ $landingPage->section3_background_color ?? '#DCE8FD' }}" title="Choose background color">
                                                            <input type="text" class="form-control" id="section3_background_color_text" value="{{ $landingPage->section3_background_color ?? '#DCE8FD' }}" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section Title -->
                                            <div class="mb-3">
                                                <label for="section3_title" class="form-label">Judul Section</label>
                                                <input type="text" class="form-control" id="section3_title" name="section3_title" value="{{ $landingPage->section3_title }}" placeholder="Masukkan judul section">
                                            </div>

                                            <!-- Sub Section Title -->
                                            <div class="mb-3">
                                                <label for="section3_sub_title" class="form-label">Sub Judul Section</label>
                                                <input type="text" class="form-control" id="section3_sub_title" name="section3_sub_title" value="{{ $landingPage->section3_sub_title }}" placeholder="Masukkan sub judul section">
                                            </div>

                                            <!-- Image Upload -->
                                            <div class="mb-3">
                                                <label for="section3_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                                <input type="file" class="form-control" id="section3_image" name="section3_image" accept="image/*">
                                                @if ($landingPage->section3_image)
                                                    <div class="mt-2">
                                                        <img src="{{ Storage::url($landingPage->section3_image) }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                                        <div class="form-text">Gambar saat ini</div>
                                                    </div>
                                                @endif
                                                <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                                <div id="section3_image_preview" class="mt-2" style="display: none;">
                                                    <img id="section3_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                                </div>
                                            </div>

                                            <!-- Content Description -->
                                            <div class="mb-3">
                                                <label for="section3_content" class="form-label">Keterangan</label>
                                                <div id="section3_content" style="height: 200px;">{!! $landingPage->section3_content !!}</div>
                                                <input type="hidden" id="section3_content_hidden" name="section3_content" value="{{ $landingPage->section3_content }}">
                                                <div class="form-text">Deskripsi konten untuk section ini</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 4 Content -->
                            <div class="tab-pane fade" id="v-pills-section4" role="tabpanel" aria-labelledby="v-pills-section4-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-image me-2"></i> Landing Page Section 4 <span class="badge bg-secondary">Optional</span></h4>

                                <div class="card">
                                    <div class="card-body">
                                        <!-- Section 4 Enable Toggle -->
                                        <div class="alert alert-info d-flex align-items-center mb-4">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="section4_enabled" name="section4_enabled"
                                                       {{ $landingPage->section4_enabled ? 'checked' : '' }} onchange="toggleSectionFields(4)">
                                                <label class="form-check-label" for="section4_enabled">
                                                    <i class="bx bx-toggle-left me-1"></i>Aktifkan Section 4
                                                </label>
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="mb-0">Aktifkan untuk menampilkan section 4 di landing page</small>
                                            </div>
                                        </div>

                                        <div id="section4_fields" class="{{ !$landingPage->section4_enabled ? 'disabled-section' : '' }}">
                                            <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                            <!-- Product Selection and Background Color -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="section4_product_id" class="form-label">Produk</label>
                                                        <input type="text" class="form-control" id="section4_product_display"
                                                               value="{{ $landingPage->section4Product ? $landingPage->section4Product->name . ' - Rp ' . number_format($landingPage->section4Product->regular_price, 0, ',', '.') : 'Produk akan mengikuti pilihan di Section 1' }}"
                                                               placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                        <input type="hidden" id="section4_product_id" name="section4_product_id" value="{{ $landingPage->section4_product_id }}">
                                                        <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <!-- Background Color -->
                                                    <div class="mb-3">
                                                        <label for="section4_background_color" class="form-label">Background Color</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="section4_background_color" name="section4_background_color" value="{{ $landingPage->section4_background_color ?? '#DCE8FD' }}" title="Choose background color">
                                                            <input type="text" class="form-control" id="section4_background_color_text" value="{{ $landingPage->section4_background_color ?? '#DCE8FD' }}" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section Title -->
                                            <div class="mb-3">
                                                <label for="section4_title" class="form-label">Judul Section</label>
                                                <input type="text" class="form-control" id="section4_title" name="section4_title" value="{{ $landingPage->section4_title }}" placeholder="Masukkan judul section">
                                            </div>

                                            <!-- Sub Section Title -->
                                            <div class="mb-3">
                                                <label for="section4_sub_title" class="form-label">Sub Judul Section</label>
                                                <input type="text" class="form-control" id="section4_sub_title" name="section4_sub_title" value="{{ $landingPage->section4_sub_title }}" placeholder="Masukkan sub judul section">
                                            </div>

                                            <!-- Image Upload -->
                                            <div class="mb-3">
                                                <label for="section4_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                                <input type="file" class="form-control" id="section4_image" name="section4_image" accept="image/*">
                                                @if ($landingPage->section4_image)
                                                    <div class="mt-2">
                                                        <img src="{{ Storage::url($landingPage->section4_image) }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                                        <div class="form-text">Gambar saat ini</div>
                                                    </div>
                                                @endif
                                                <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                                <div id="section4_image_preview" class="mt-2" style="display: none;">
                                                    <img id="section4_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                                </div>
                                            </div>

                                            <!-- Content Description -->
                                            <div class="mb-3">
                                                <label for="section4_content" class="form-label">Keterangan</label>
                                                <div id="section4_content" style="height: 200px;">{!! $landingPage->section4_content !!}</div>
                                                <input type="hidden" id="section4_content_hidden" name="section4_content" value="{{ $landingPage->section4_content }}">
                                                <div class="form-text">Deskripsi konten untuk section ini</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 5 Content -->
                            <div class="tab-pane fade" id="v-pills-section5" role="tabpanel" aria-labelledby="v-pills-section5-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-file me-2"></i> Landing Page Section 5 <span class="badge bg-secondary">Optional</span></h4>

                                <div class="card">
                                    <div class="card-body">
                                        <!-- Section 5 Enable Toggle -->
                                        <div class="alert alert-info d-flex align-items-center mb-4">
                                            <div class="form-check form-switch me-3">
                                                <input class="form-check-input" type="checkbox" id="section5_enabled" name="section5_enabled"
                                                       {{ $landingPage->section5_enabled ? 'checked' : '' }} onchange="toggleSectionFields(5)">
                                                <label class="form-check-label" for="section5_enabled">
                                                    <i class="bx bx-toggle-left me-1"></i>Aktifkan Section 5
                                                </label>
                                            </div>
                                            <div class="flex-grow-1">
                                                <small class="mb-0">Aktifkan untuk menampilkan section 5 di landing page</small>
                                            </div>
                                        </div>

                                        <div id="section5_fields" class="{{ !$landingPage->section5_enabled ? 'disabled-section' : '' }}">
                                            <h5 class="card-title"><i class="bx bx-package me-2"></i> Product</h5>

                                            <!-- Product Selection and Background Color -->
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label for="section5_product_id" class="form-label">Produk</label>
                                                        <input type="text" class="form-control" id="section5_product_display"
                                                               value="{{ $landingPage->section5Product ? $landingPage->section5Product->name . ' - Rp ' . number_format($landingPage->section5Product->regular_price, 0, ',', '.') : 'Produk akan mengikuti pilihan di Section 1' }}"
                                                               placeholder="Produk akan mengikuti pilihan di Section 1" readonly>
                                                        <input type="hidden" id="section5_product_id" name="section5_product_id" value="{{ $landingPage->section5_product_id }}">
                                                        <div class="form-text">Produk akan otomatis mengikuti produk yang dipilih di Section 1</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <!-- Background Color -->
                                                    <div class="mb-3">
                                                        <label for="section5_background_color" class="form-label">Background Color</label>
                                                        <div class="input-group">
                                                            <input type="color" class="form-control form-control-color" id="section5_background_color" name="section5_background_color" value="{{ $landingPage->section5_background_color ?? '#DCE8FD' }}" title="Choose background color">
                                                            <input type="text" class="form-control" id="section5_background_color_text" value="{{ $landingPage->section5_background_color ?? '#DCE8FD' }}" readonly>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Section Title -->
                                            <div class="mb-3">
                                                <label for="section5_title" class="form-label">Judul Section</label>
                                                <input type="text" class="form-control" id="section5_title" name="section5_title" value="{{ $landingPage->section5_title }}" placeholder="Masukkan judul section">
                                            </div>

                                            <!-- Sub Section Title -->
                                            <div class="mb-3">
                                                <label for="section5_sub_title" class="form-label">Sub Judul Section</label>
                                                <input type="text" class="form-control" id="section5_sub_title" name="section5_sub_title" value="{{ $landingPage->section5_sub_title }}" placeholder="Masukkan sub judul section">
                                            </div>

                                            <!-- Image Upload -->
                                            <div class="mb-3">
                                                <label for="section5_image" class="form-label">Upload Gambar (564 x 2030)</label>
                                                <input type="file" class="form-control" id="section5_image" name="section5_image" accept="image/*">
                                                @if ($landingPage->section5_image)
                                                    <div class="mt-2">
                                                        <img src="{{ Storage::url($landingPage->section5_image) }}" alt="Current Image" class="img-thumbnail" style="max-width: 200px;">
                                                        <div class="form-text">Gambar saat ini</div>
                                                    </div>
                                                @endif
                                                <div class="form-text">Format yang didukung: JPG, PNG, GIF. Ukuran maksimal: 2MB</div>
                                                <div id="section5_image_preview" class="mt-2" style="display: none;">
                                                    <img id="section5_preview_img" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                                                </div>
                                            </div>

                                            <!-- Content Description -->
                                            <div class="mb-3">
                                                <label for="section5_content" class="form-label">Keterangan</label>
                                                <div id="section5_content" style="height: 200px;">{!! $landingPage->section5_content !!}</div>
                                                <input type="hidden" id="section5_content_hidden" name="section5_content" value="{{ $landingPage->section5_content }}">
                                                <div class="form-text">Deskripsi konten untuk section ini</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 6 Content -->
                            <div class="tab-pane fade" id="v-pills-section6" role="tabpanel" aria-labelledby="v-pills-section6-tab" tabindex="0">
                                <h4 class="mb-4"><i class="bx bx-shopping-cart me-2"></i> Landing Page Section 6 - Form Order/Checkout</h4>

                                <!-- Product & Background Configuration -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-cog me-2"></i> Konfigurasi Dasar</h5>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <!-- Product Selection -->
                                                <div class="mb-3">
                                                    <label for="section6_product_id" class="form-label">Pilih Produk untuk Checkout</label>
                                                    <select class="form-select" id="section6_product_id" name="section6_product_id">
                                                        <option value="">-- Pilih Produk (Opsional) --</option>
                                                        @foreach ($products as $product)
                                                            <option value="{{ $product->id }}"
                                                                    {{ $landingPage->section6 && $landingPage->section6->product_id == $product->id ? 'selected' : '' }}
                                                                    data-price="{{ $product->regular_price }}" data-discount="{{ $product->discount_price }}">
                                                                {{ $product->name }} - Rp {{ number_format($product->regular_price, 0, ',', '.') }}
                                                                @if ($product->discount_price)
                                                                    (Diskon: Rp {{ number_format($product->discount_price, 0, ',', '.') }})
                                                                @endif
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <div class="form-text">Produk yang akan ditampilkan di form checkout</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Background Color -->
                                                <div class="mb-3">
                                                    <label for="section6_background_color" class="form-label">Background Color <span class="text-danger">*</span></label>
                                                    <div class="input-group">
                                                        <input type="color" class="form-control form-control-color" id="section6_background_color" name="section6_background_color" value="{{ $landingPage->section6->background_color ?? '#DCE8FD' }}" title="Choose background color">
                                                        <input type="text" class="form-control" id="section6_background_color_text" value="{{ $landingPage->section6->background_color ?? '#DCE8FD' }}" readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Information Form Fields -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-user me-2"></i>Form Pemesanan Customer</h5>

                                        <div class="row">
                                            <!-- Customer Name -->
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_customer_name_label" class="form-label">Label Nama Anda <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_customer_name_label" name="section6_customer_name_label" value="{{ $landingPage->section6->customer_name_label ?? 'Nama Anda' }}" placeholder="Masukkan label untuk nama">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Customer Name Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_customer_name_placeholder" class="form-label">Placeholder Nama Anda</label>
                                                    <input type="text" class="form-control" id="section6_customer_name_placeholder" name="section6_customer_name_placeholder" value="{{ $landingPage->section6->customer_name_placeholder ?? 'Masukkan nama lengkap' }}" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- WhatsApp Number -->
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_whatsapp_label" class="form-label">Label No WhatsApp Anda <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_whatsapp_label" name="section6_whatsapp_label" value="{{ $landingPage->section6->whatsapp_label ?? 'No WhatsApp Anda' }}" placeholder="Masukkan label untuk WhatsApp">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- WhatsApp Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_whatsapp_placeholder" class="form-label">Placeholder No WhatsApp Anda</label>
                                                    <input type="text" class="form-control" id="section6_whatsapp_placeholder" name="section6_whatsapp_placeholder" value="{{ $landingPage->section6->whatsapp_placeholder ?? '08xxxxxxxxxx' }}" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <!-- Complete Address -->
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_address_label" class="form-label">Label Alamat Lengkap <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_address_label" name="section6_address_label" value="{{ $landingPage->section6->address_label ?? 'Alamat Lengkap' }}" placeholder="Masukkan label untuk alamat">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Address Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_address_placeholder" class="form-label">Placeholder Alamat Lengkap</label>
                                                    <input type="text" class="form-control" id="section6_address_placeholder" name="section6_address_placeholder" value="{{ $landingPage->section6->address_placeholder ?? 'Jl. Contoh No. 123, RT/RW 01/02' }}" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Location & Notes Configuration -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-map me-2"></i>Lokasi & Catatan</h5>

                                        <!-- City/District -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_city_label" class="form-label">Label Nama Kota / Kecamatan <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_city_label" name="section6_city_label" value="{{ $landingPage->section6->city_label ?? 'Nama Kota / Kecamatan' }}" placeholder="Masukkan label untuk kota">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_city_default" class="form-label">Pilihan Default Kota</label>
                                                    <select class="form-select" id="section6_city_default" name="section6_city_default">
                                                        <option value="">Pilih kota default</option>
                                                        <option value="jakarta" {{ $landingPage->section6 && $landingPage->section6->city_default == 'jakarta' ? 'selected' : '' }}>Jakarta</option>
                                                        <option value="bandung" {{ $landingPage->section6 && $landingPage->section6->city_default == 'bandung' ? 'selected' : '' }}>Bandung</option>
                                                        <option value="surabaya" {{ $landingPage->section6 && $landingPage->section6->city_default == 'surabaya' ? 'selected' : '' }}>Surabaya</option>
                                                        <option value="medan" {{ $landingPage->section6 && $landingPage->section6->city_default == 'medan' ? 'selected' : '' }}>Medan</option>
                                                        <option value="semarang" {{ $landingPage->section6 && $landingPage->section6->city_default == 'semarang' ? 'selected' : '' }}>Semarang</option>
                                                        <option value="yogyakarta" {{ $landingPage->section6 && $landingPage->section6->city_default == 'yogyakarta' ? 'selected' : '' }}>Yogyakarta</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Order Notes -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_notes_label" class="form-label">Label Catatan Pemesanan</label>
                                                    <input type="text" class="form-control" id="section6_notes_label" name="section6_notes_label" value="{{ $landingPage->section6->notes_label ?? 'Catatan Pemesanan' }}" placeholder="Masukkan label untuk catatan">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Notes Placeholder -->
                                                <div class="mb-3">
                                                    <label for="section6_notes_placeholder" class="form-label">Placeholder Catatan Pemesanan</label>
                                                    <input type="text" class="form-control" id="section6_notes_placeholder" name="section6_notes_placeholder" value="{{ $landingPage->section6->notes_placeholder ?? 'Catatan khusus untuk pesanan (opsional)' }}" placeholder="Masukkan placeholder">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Shipping & Payment Configuration -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-truck me-2"></i>Pengiriman & Pembayaran</h5>

                                        <!-- Shipping Options -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_shipping_label" class="form-label">Label Pengiriman <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="section6_shipping_label" name="section6_shipping_label" value="{{ $landingPage->section6->shipping_label ?? 'Pengiriman' }}" placeholder="Masukkan label untuk pengiriman">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_shipping_service_label" class="form-label">Label Pilih Layanan</label>
                                                    <input type="text" class="form-control" id="section6_shipping_service_label" name="section6_shipping_service_label" value="{{ $landingPage->section6->shipping_service_label ?? 'Pilih Layanan' }}" placeholder="Masukkan label untuk layanan">
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Payment Method -->
                                        <div class="mb-3">
                                            <label for="section6_payment_label" class="form-label">Label Payment Method <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="section6_payment_label" name="section6_payment_label" value="{{ $landingPage->section6->payment_label ?? 'Metode Pembayaran' }}" placeholder="Masukkan label untuk metode pembayaran">
                                        </div>

                                        <!-- Payment Options -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="section6_enable_bank_transfer" name="section6_enable_bank_transfer" {{ $landingPage->section6 && $landingPage->section6->enable_bank_transfer ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="section6_enable_bank_transfer">
                                                        <i class="bx bx-credit-card me-1"></i>Aktifkan Bank Transfer
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check form-switch">
                                                    <input class="form-check-input" type="checkbox" id="section6_enable_cod" name="section6_enable_cod" {{ $landingPage->section6 && $landingPage->section6->enable_cod ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="section6_enable_cod">
                                                        <i class="bx bx-money me-1"></i>Aktifkan COD (Bayar di Tempat)
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Order Summary & Pricing -->
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <h5 class="card-title"><i class="bx bx-receipt me-2"></i>Ringkasan Pesanan & Harga</h5>

                                        <!-- Product Pricing -->
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="section6_product_price" class="form-label">Harga Produk (Rp)</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number" class="form-control" id="section6_product_price" name="section6_product_price" placeholder="155000" value="{{ $landingPage->section6->product_price ?? '155000' }}" min="0">
                                                    </div>
                                                    <div class="form-text">Harga produk yang akan ditampilkan</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="section6_shipping_cost" class="form-label">Biaya Ongkir (Rp)</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number" class="form-control" id="section6_shipping_cost" name="section6_shipping_cost" placeholder="15000" value="{{ $landingPage->section6->shipping_cost ?? '15000' }}" min="0">
                                                    </div>
                                                    <div class="form-text">Estimasi biaya pengiriman</div>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label for="section6_total_price" class="form-label">Total Harga (Rp)</label>
                                                    <div class="input-group">
                                                        <span class="input-group-text">Rp</span>
                                                        <input type="number" class="form-control" id="section6_total_price" name="section6_total_price" placeholder="170000" value="{{ $landingPage->section6->total_price ?? '170000' }}" min="0" readonly>
                                                    </div>
                                                    <div class="form-text">Total otomatis dihitung</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Order Button -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="section6_order_button_text" class="form-label">Text Tombol Pesan</label>
                                                    <input type="text" class="form-control" id="section6_order_button_text" name="section6_order_button_text" value="{{ $landingPage->section6->order_button_text ?? 'Beli Sekarang' }}" placeholder="Masukkan text tombol">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Section 1 Button -->
                                                <div class="mb-3">
                                                    <label for="section6_section1_button_text" class="form-label">Text Tombol Scroll ke Section 1</label>
                                                    <input type="text" class="form-control" id="section6_section1_button_text" name="section6_section1_button_text" value="{{ $landingPage->section6->section1_button_text ?? 'Lihat Detail Produk' }}" placeholder="Masukkan text tombol">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <div>
                        <a href="{{ route('landingPage.preview', $landingPage->slug) }}" target="_blank" class="btn btn-outline-primary">
                            <i class="bx bx-show me-1"></i>Preview Landing Page
                        </a>
                    </div>
                    <div>
                        <a href="{{ route('landingPage.index') }}" class="btn btn-secondary me-2">
                            <i class="bx bx-arrow-back me-1"></i>Kembali
                        </a>
                        <button type="button" class="btn btn-info me-2" id="testContentBtn" title="Test HTML Content">
                            <i class="bx bx-test-tube me-1"></i>Test HTML
                        </button>
                        <button type="submit" class="btn btn-primary" id="saveBtn">
                            <i class="bx bx-save me-1"></i>Update Landing Page
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('style')
    <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet" />
    <style>
        /* Sidebar Navigation Improvements */
        .sidebar-nav-container {
            position: sticky;
            top: 20px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 12px;
            border: 1px solid #e7e7ff;
        }

        .section-number {
            background: #696cff;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            font-weight: 600;
            flex-shrink: 0;
        }

        .section-title {
            font-weight: 500;
            font-size: 14px;
        }

        .optional-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }

        .section-nav.active .section-number {
            background: white;
            color: #696cff;
        }

        .section-nav.active .optional-badge {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white;
        }

        .section-nav {
            border-radius: 10px;
            border: 1px solid #e7e7ff;
            padding: 12px 16px;
            min-height: 50px;
        }

        .section-nav:hover {
            background-color: #f0f2ff;
            border-color: #696cff;
        }

        .section-nav.active {
            background-color: #696cff;
            border-color: #696cff;
            color: white;
        }

        /* Switch Improvements */
        .form-check-input:checked {
            background-color: #696cff;
            border-color: #696cff;
        }

        .form-check-input:focus {
            outline: none;
        }

        /* Validation error styles */
        .form-control.is-invalid {
            border-color: #dc3545;
        }

        .invalid-feedback {
            display: block;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }

        /* Disabled Section Styling */
        .disabled-section {
            opacity: 0.6;
            pointer-events: none;
            position: relative;
        }

        .disabled-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.7);
            z-index: 1;
            border-radius: 8px;
        }

        .disabled-field {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
        }

        /* Alert styling improvements */
        .alert-info {
            background-color: #e7f3ff;
            border-color: #b8daff;
            color: #0c5460;
        }

        /* Form control color improvements */
        .form-control-color {
            width: 60px;
            height: 38px;
            padding: 0.375rem 0.5rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.js"></script>
    <script>
        $(document).ready(function() {
            // Make editors available globally for toggleSectionFields function
            window.editors = {};

            // Initialize Quill editors
            const editors = {};
            const initializedEditors = new Set();

            console.log('Landing Page Edit: Starting initialization...');

            // Function to safely initialize a Quill editor
            function initializeQuillEditor(containerId, editorKey, options = {}) {
                try {
                    // Check if Quill is available
                    if (typeof Quill === 'undefined') {
                        console.error('Quill is not loaded');
                        return null;
                    }

                    const container = document.getElementById(containerId);
                    if (!container) {
                        console.warn(`Container ${containerId} not found`);
                        return null;
                    }

                    // Check if already initialized
                    if (initializedEditors.has(editorKey)) {
                        return editors[editorKey];
                    }

                    const defaultOptions = {
                        theme: 'snow',
                        placeholder: options.placeholder || 'Masukkan konten...',
                        modules: {
                            toolbar: [
                                ['bold', 'italic', 'underline', 'strike'],
                                ['blockquote', 'code-block'],
                                [{
                                    'header': 1
                                }, {
                                    'header': 2
                                }],
                                [{
                                    'list': 'ordered'
                                }, {
                                    'list': 'bullet'
                                }],
                                [{
                                    'script': 'sub'
                                }, {
                                    'script': 'super'
                                }],
                                [{
                                    'indent': '-1'
                                }, {
                                    'indent': '+1'
                                }],
                                [{
                                    'direction': 'rtl'
                                }],
                                [{
                                    'size': ['small', false, 'large', 'huge']
                                }],
                                [{
                                    'header': [1, 2, 3, 4, 5, 6, false]
                                }],
                                [{
                                    'color': []
                                }, {
                                    'background': []
                                }],
                                [{
                                    'font': []
                                }],
                                [{
                                    'align': []
                                }],
                                ['clean'],
                                ['link', 'image']
                            ]
                        }
                    };

                    if (options.modules) {
                        defaultOptions.modules = {
                            ...defaultOptions.modules,
                            ...options.modules
                        };
                    }

                    const editor = new Quill(`#${containerId}`, defaultOptions);
                    editors[editorKey] = editor;
                    window.editors[editorKey] = editor; // Also set in global scope
                    initializedEditors.add(editorKey);

                    // Store reference in the container element for easier access
                    container.__quill = editor;

                    // Load existing content if available
                    const hiddenFieldId = `${editorKey}_hidden`;
                    const hiddenField = document.getElementById(hiddenFieldId);
                    if (hiddenField && hiddenField.value) {
                        try {
                            // Set content using innerHTML to preserve HTML formatting
                            editor.root.innerHTML = hiddenField.value;
                            console.log(`Loaded existing content for ${editorKey}:`, hiddenField.value.substring(0, 100) + '...');
                        } catch (error) {
                            console.warn(`Error loading content for ${editorKey}:`, error);
                        }
                    }

                    // Add content change listener to automatically update hidden field
                    editor.on('text-change', function(delta, oldDelta, source) {
                        if (hiddenField) {
                            const htmlContent = editor.root.innerHTML;
                            hiddenField.value = htmlContent;
                            console.log(`Auto-updated hidden field ${hiddenFieldId} with content:`, htmlContent.substring(0, 100) + '...');
                        }
                    });

                    console.log(`Successfully initialized Quill editor: ${editorKey}`);
                    return editor;
                } catch (error) {
                    console.error(`Error initializing Quill editor ${editorKey}:`, error);
                    return null;
                }
            }

            // Make initializeQuillEditor available globally
            window.initializeQuillEditor = initializeQuillEditor;

            // Initialize all editors after DOM is ready
            function initializeAllEditors() {
                const editorConfigs = [{
                        containerId: 'content_description',
                        editorKey: 'content_description',
                        placeholder: 'Masukkan deskripsi konten Section 1...'
                    },
                    {
                        containerId: 'section2_content',
                        editorKey: 'section2_content',
                        placeholder: 'Masukkan konten Section 2...'
                    },
                    {
                        containerId: 'section3_content',
                        editorKey: 'section3_content',
                        placeholder: 'Masukkan konten Section 3...'
                    },
                    {
                        containerId: 'section4_content',
                        editorKey: 'section4_content',
                        placeholder: 'Masukkan konten Section 4...'
                    },
                    {
                        containerId: 'section5_content',
                        editorKey: 'section5_content',
                        placeholder: 'Masukkan konten Section 5...'
                    }
                ];

                editorConfigs.forEach(config => {
                    setTimeout(() => {
                        initializeQuillEditor(config.containerId, config.editorKey, {
                            placeholder: config.placeholder
                        });
                    }, 100);
                });
            }

            // Initialize all editors after page load
            setTimeout(initializeAllEditors, 500);

            // Initialize optional sections as disabled by default after editors are ready
            setTimeout(() => {
                console.log('Checking disabled sections...');
                // Check and disable sections 3, 4, 5 if they are not enabled
                for (let i = 3; i <= 5; i++) {
                    const checkbox = document.getElementById(`section${i}_enabled`);
                    if (checkbox && !checkbox.checked) {
                        console.log(`Section ${i} is disabled, applying disabled state`);
                        toggleSectionFields(i);
                    }
                }
            }, 1500);

            // Function to update all hidden fields with editor content
            function updateAllHiddenFields() {
                console.log('Updating all hidden fields with editor content...');

                // Check local editors object
                Object.keys(editors).forEach(editorKey => {
                    const hiddenFieldId = `${editorKey}_hidden`;
                    const hiddenField = document.getElementById(hiddenFieldId);

                    if (hiddenField && editors[editorKey]) {
                        try {
                            const htmlContent = editors[editorKey].root.innerHTML;
                            hiddenField.value = htmlContent;
                            console.log(`Updated hidden field ${hiddenFieldId} with content (${htmlContent.length} chars):`, htmlContent.substring(0, 100) + '...');
                        } catch (error) {
                            console.warn(`Error updating hidden field ${hiddenFieldId}:`, error);
                        }
                    }
                });

                // Also check global window.editors object
                if (window.editors) {
                    Object.keys(window.editors).forEach(editorKey => {
                        const hiddenFieldId = `${editorKey}_hidden`;
                        const hiddenField = document.getElementById(hiddenFieldId);

                        if (hiddenField && window.editors[editorKey]) {
                            try {
                                const htmlContent = window.editors[editorKey].root.innerHTML;
                                if (hiddenField.value !== htmlContent) {
                                    hiddenField.value = htmlContent;
                                    console.log(`Updated hidden field ${hiddenFieldId} with content from global editors (${htmlContent.length} chars):`, htmlContent.substring(0, 100) + '...');
                                }
                            } catch (error) {
                                console.warn(`Error updating hidden field ${hiddenFieldId} from global editors:`, error);
                            }
                        }
                    });
                }

                // Manual check for any Quill instances attached to containers
                const editorContainers = ['content_description', 'section2_content', 'section3_content', 'section4_content', 'section5_content'];
                editorContainers.forEach(containerId => {
                    const container = document.getElementById(containerId);
                    const hiddenFieldId = `${containerId}_hidden`;
                    const hiddenField = document.getElementById(hiddenFieldId);

                    if (container && container.__quill && hiddenField) {
                        try {
                            const htmlContent = container.__quill.root.innerHTML;
                            if (hiddenField.value !== htmlContent) {
                                hiddenField.value = htmlContent;
                                console.log(`Updated hidden field ${hiddenFieldId} with content from container.__quill (${htmlContent.length} chars):`, htmlContent.substring(0, 100) + '...');
                            }
                        } catch (error) {
                            console.warn(`Error updating hidden field ${hiddenFieldId} from container.__quill:`, error);
                        }
                    }
                });

                console.log('Finished updating all hidden fields.');
            }

            // Color picker functionality
            $('#background_color').on('change', function() {
                $('#background_color_text').val($(this).val());
            });

            $('#section2_background_color').on('change', function() {
                $('#section2_background_color_text').val($(this).val());
            });

            $('#section3_background_color').on('change', function() {
                $('#section3_background_color_text').val($(this).val());
            });

            $('#section4_background_color').on('change', function() {
                $('#section4_background_color_text').val($(this).val());
            });

            $('#section5_background_color').on('change', function() {
                $('#section5_background_color_text').val($(this).val());
            });

            $('#section6_background_color').on('change', function() {
                $('#section6_background_color_text').val($(this).val());
            });

            // Image preview functionality
            $('#section_image').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview_img').attr('src', e.target.result);
                        $('#image_preview').show();
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Section 2-5 image previews
            for (let i = 2; i <= 5; i++) {
                $(`#section${i}_image`).on('change', function() {
                    const file = this.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            $(`#section${i}_preview_img`).attr('src', e.target.result);
                            $(`#section${i}_image_preview`).show();
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Auto-sync product selection from Section 1 to other sections
            $('#product_id').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const productId = $(this).val();
                const productText = selectedOption.text();

                // Update section 2-5 product displays
                for (let i = 2; i <= 5; i++) {
                    if (productId) {
                        $(`#section${i}_product_display`).val(productText);
                        $(`#section${i}_product_id`).val(productId);
                    } else {
                        $(`#section${i}_product_display`).val('Produk akan mengikuti pilihan di Section 1');
                        $(`#section${i}_product_id`).val(''); // Will be handled during form submission
                    }
                }
            });

            // Section 6 price calculation
            function calculateTotal() {
                const productPrice = parseFloat($('#section6_product_price').val()) || 0;
                const shippingCost = parseFloat($('#section6_shipping_cost').val()) || 0;
                const total = productPrice + shippingCost;
                $('#section6_total_price').val(total);
            }

            $('#section6_product_price, #section6_shipping_cost').on('input', calculateTotal);

            // Tab change handler to update hidden fields
            $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function(e) {
                // Update all hidden fields when changing tabs
                updateAllHiddenFields();
            });

            // Test content button to verify HTML is being stored and loaded
            $('#testContentBtn').on('click', function() {
                updateAllHiddenFields();

                let testResults = [];
                const editorContainers = ['content_description', 'section2_content', 'section3_content', 'section4_content', 'section5_content'];

                editorContainers.forEach(containerId => {
                    const hiddenField = document.getElementById(`${containerId}_hidden`);
                    const editor = editors[containerId] || window.editors[containerId];

                    let fieldStatus = '';
                    let editorStatus = '';

                    if (hiddenField && hiddenField.value) {
                        const hasHtml = /<[^>]+>/.test(hiddenField.value);
                        fieldStatus = hasHtml ? 'HTML detected ✅' : 'Plain text only ❌';
                    } else {
                        fieldStatus = 'Empty';
                    }

                    if (editor) {
                        const editorContent = editor.root.innerHTML;
                        const hasEditorHtml = /<[^>]+>/.test(editorContent);
                        editorStatus = hasEditorHtml ? 'Editor has HTML ✅' : 'Editor plain text ❌';
                    } else {
                        editorStatus = 'Editor not found ❌';
                    }

                    testResults.push(`<strong>${containerId}:</strong><br>Hidden Field: ${fieldStatus}<br>Editor: ${editorStatus}`);
                });

                Swal.fire({
                    title: 'Test HTML Content (Edit Mode)',
                    html: testResults.join('<br><br>'),
                    icon: 'info',
                    confirmButtonColor: '#696cff',
                    footer: 'HTML tags should be preserved for formatting',
                    width: '600px'
                });
            });

            // Form submission
            $('#landingPageForm').on('submit', function(e) {
                e.preventDefault();

                // Update all hidden fields with editor content before submission
                updateAllHiddenFields();

                // Show loading state
                const submitButton = $('#saveBtn, button[type="submit"]').first();
                const originalText = submitButton.html();
                submitButton.prop('disabled', true).html('<i class="bx bx-loader-alt bx-spin me-1"></i>Mengupdate...');

                // Create FormData object for file uploads
                const formData = new FormData(this);

                // Handle product_id fields - convert empty strings to null or remove them
                const productIdFields = ['product_id', 'section2_product_id', 'section3_product_id', 'section4_product_id', 'section5_product_id', 'section6_product_id'];
                productIdFields.forEach(field => {
                    const value = formData.get(field);
                    if (value === '' || value === null) {
                        formData.delete(field); // Remove empty fields, backend will handle as null
                    }
                });

                // Handle checkbox values properly - ensure they are sent as boolean values
                const checkboxFields = ['section3_enabled', 'section4_enabled', 'section5_enabled', 'section6_enable_bank_transfer', 'section6_enable_cod'];
                checkboxFields.forEach(field => {
                    const checkbox = document.getElementById(field);
                    if (checkbox) {
                        // Remove the existing value and set proper boolean
                        formData.delete(field);
                        formData.append(field, checkbox.checked ? '1' : '0');
                    }
                });

                // Handle pixel events data - convert JSON string back to array (similar to create form)
                const pixelEventsValue = formData.get('pixel_events');
                const pixelEventParametersValue = formData.get('pixel_event_parameters');

                // Remove the original JSON string fields
                formData.delete('pixel_events');
                formData.delete('pixel_event_parameters');

                if (pixelEventsValue && pixelEventsValue.trim() !== '') {
                    try {
                        const pixelEventsArray = JSON.parse(pixelEventsValue);

                        // Add each event as a separate form field for array handling
                        if (Array.isArray(pixelEventsArray) && pixelEventsArray.length > 0) {
                            pixelEventsArray.forEach((event, index) => {
                                formData.append(`pixel_events[${index}]`, event);
                            });
                        }
                    } catch (error) {
                        console.warn('Error parsing pixel events:', error);
                        // Don't add anything if parsing fails
                    }
                }

                if (pixelEventParametersValue && pixelEventParametersValue.trim() !== '') {
                    try {
                        const pixelEventParametersObj = JSON.parse(pixelEventParametersValue);

                        // Add each parameter as a separate form field for object handling
                        if (typeof pixelEventParametersObj === 'object' && pixelEventParametersObj !== null) {
                            Object.keys(pixelEventParametersObj).forEach(key => {
                                if (typeof pixelEventParametersObj[key] === 'object') {
                                    formData.append(`pixel_event_parameters[${key}]`, JSON.stringify(pixelEventParametersObj[key]));
                                } else {
                                    formData.append(`pixel_event_parameters[${key}]`, pixelEventParametersObj[key]);
                                }
                            });
                        }
                    } catch (error) {
                        console.warn('Error parsing pixel event parameters:', error);
                        // Don't add anything if parsing fails
                    }
                }

                // Debug: Log all editor content being submitted
                console.log('Submitting form with editor content:');
                Object.keys(editors).forEach(editorKey => {
                    const hiddenFieldId = `${editorKey}_hidden`;
                    const hiddenField = document.getElementById(hiddenFieldId);
                    if (hiddenField) {
                        console.log(`${hiddenFieldId}:`, hiddenField.value);
                    }
                });

                // Also log global editors content
                if (window.editors) {
                    Object.keys(window.editors).forEach(editorKey => {
                        const hiddenFieldId = `${editorKey}_hidden`;
                        const hiddenField = document.getElementById(hiddenFieldId);
                        if (hiddenField) {
                            console.log(`${hiddenFieldId} (from window.editors):`, hiddenField.value);
                        }
                    });
                }

                // Debug: Log form data being sent
                console.log('FormData entries:');
                for (let [key, value] of formData.entries()) {
                    if (key.includes('content') || key.includes('_hidden')) {
                        console.log(`${key}:`, typeof value === 'string' ? value.substring(0, 200) + '...' : value);
                    }
                }

                // Submit form via AJAX
                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil!',
                                text: response.message,
                                confirmButtonColor: '#696cff'
                            }).then(() => {
                                window.location.href = '{{ route('landingPage.index') }}';
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Gagal!',
                                text: response.message,
                                confirmButtonColor: '#696cff'
                            });
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'Terjadi kesalahan saat mengupdate landing page.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            const errors = Object.values(xhr.responseJSON.errors).flat();
                            errorMessage = errors.join('<br>');
                        }

                        Swal.fire({
                            icon: 'error',
                            title: 'Gagal!',
                            html: errorMessage,
                            confirmButtonColor: '#696cff'
                        });
                    },
                    complete: function() {
                        submitButton.prop('disabled', false).html(originalText);
                    }
                });
            });
        });

        // Function to toggle section fields enabled/disabled state
        function toggleSectionFields(sectionNumber) {
            console.log(`Toggling section ${sectionNumber}`);

            const checkbox = document.getElementById(`section${sectionNumber}_enabled`);
            const fieldsContainer = document.getElementById(`section${sectionNumber}_fields`);
            const isEnabled = checkbox.checked;

            console.log(`Section ${sectionNumber} - Enabled: ${isEnabled}`);

            // Get all form inputs within the section
            const inputs = fieldsContainer.querySelectorAll('input, select, textarea');

            // Enable/disable all form inputs
            inputs.forEach(input => {
                if (input.type !== 'hidden') {
                    input.disabled = !isEnabled;
                    if (isEnabled) {
                        input.classList.remove('disabled-field');
                    } else {
                        input.classList.add('disabled-field');
                        // Clear values when disabled (but don't clear editor content)
                        if (input.type === 'file') {
                            input.value = '';
                        } else if (input.type === 'color') {
                            input.value = '#DCE8FD';
                        } else if (input.tagName === 'SELECT') {
                            input.selectedIndex = 0;
                        } else if (input.type !== 'hidden' && !input.id.includes('_content_hidden')) {
                            input.value = '';
                        }
                    }
                }
            });

            // Handle Quill editors specifically for this section
            const sectionEditorKey = `section${sectionNumber}_content`;
            console.log(`Looking for editor: ${sectionEditorKey}`);

            let editorFound = false;
            let quillInstance = null;

            // Try multiple approaches to find the editor

            // 1. Check local editors object
            if (editors && editors[sectionEditorKey]) {
                console.log(`Found editor in local editors object: ${sectionEditorKey}`);
                quillInstance = editors[sectionEditorKey];
                editorFound = true;
            }

            // 2. Check global window.editors object
            if (!editorFound && window.editors && window.editors[sectionEditorKey]) {
                console.log(`Found editor in window.editors: ${sectionEditorKey}`);
                quillInstance = window.editors[sectionEditorKey];
                editorFound = true;
            }

            // 3. Check container.__quill
            if (!editorFound) {
                const editorContainer = document.getElementById(sectionEditorKey);
                if (editorContainer && editorContainer.__quill) {
                    console.log(`Found editor in container.__quill: ${sectionEditorKey}`);
                    quillInstance = editorContainer.__quill;
                    editorFound = true;
                }
            }

            // Apply enable/disable to the found editor
            if (editorFound && quillInstance) {
                try {
                    console.log(`${isEnabled ? 'Enabling' : 'Disabling'} editor: ${sectionEditorKey}`);
                    quillInstance.enable(isEnabled);

                    if (!isEnabled) {
                        // Clear editor content when disabled
                        quillInstance.setContents([]);
                        // Also clear the hidden field
                        const hiddenField = document.getElementById(`${sectionEditorKey}_hidden`);
                        if (hiddenField) {
                            hiddenField.value = '';
                            console.log(`Cleared hidden field: ${sectionEditorKey}_hidden`);
                        }
                    }
                } catch (error) {
                    console.error(`Error handling editor ${sectionEditorKey}:`, error);
                }
            } else {
                console.warn(`No Quill editor found for: ${sectionEditorKey}`);

                // If no editor found, try to initialize it if section is being enabled
                if (isEnabled && window.initializeQuillEditor) {
                    setTimeout(() => {
                        console.log(`Attempting to initialize missing editor: ${sectionEditorKey}`);
                        const newEditor = window.initializeQuillEditor(sectionEditorKey, sectionEditorKey, {
                            placeholder: `Masukkan konten Section ${sectionNumber}...`
                        });
                        if (newEditor) {
                            console.log(`Successfully initialized missing editor: ${sectionEditorKey}`);
                        }
                    }, 100);
                }
            }

            // Update visual appearance
            if (isEnabled) {
                fieldsContainer.classList.remove('disabled-section');
            } else {
                fieldsContainer.classList.add('disabled-section');
            }

            console.log(`Finished toggling section ${sectionNumber}`);
        }
    </script>
@endpush
