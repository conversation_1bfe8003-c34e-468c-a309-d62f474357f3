<?php

namespace App\Helpers;

class TimeoutHelper
{
    public static function runWithTimeout(callable $func, int $timeoutSeconds = 30)
    {
        $start = microtime(true);
        $result = null;

        try {
            $result = $func();
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }

        $elapsed = microtime(true) - $start;
        if ($elapsed > $timeoutSeconds) {
            return ['error' => 'Gagal terhubung ke server (Timeout > ' . $timeoutSeconds . ' detik)'];
        }

        return $result;
    }
}
