<?php

namespace App\Http\Controllers\DataMaster;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\EarningPotential\EarningPotentialService;
use App\Models\EarningPotential;
use Carbon\Carbon;

class EarningPotentialController extends Controller
{
    public function __construct(protected EarningPotentialService $earningPotentialService)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('data-master.earning-potential.index', [
            'title' => 'Earning Potential',
            'users' => User::where('level_user', 4)->get(),
            // 'product_codes' => DB::table('data')->distinct()->pluck('product_code')
        ]);
    }

    public function data(Request $request)
    {        
        $advId = null;
        $bulan = null;

        if ($request->has('handled_by') && !empty($request->get('handled_by'))) {
            $advId = $request->get('handled_by');
        }

        if ($request->has('bulan') && !empty($request->get('bulan'))) {
            $bulan = $request->get('bulan');
        }

        // Ambil data biaya manual
        $manualCosts = EarningPotential::select('date', 'advertising_cost', 'other_cost')
            //jika level 1 maka ambil advId dari request jika level 4 ambil dari auth
            ->when(auth()->user()->level_user == 1, fn($q) => $q->where('user_id', $advId))
            ->when(auth()->user()->level_user == 4, fn($q) => $q->where('user_id', auth()->user()->id))            
            ->when($bulan, fn($q) => $q->whereBetween('date', [
                Carbon::parse($bulan)->startOfMonth(),
                Carbon::parse($bulan)->endOfMonth()
            ]))
            ->get()
            ->keyBy('date');

        // Memanggil fungsi grid() di service
        $data = $this->earningPotentialService->grid($advId, $bulan);        

        $newData = $data->map(function ($item) use ($manualCosts) {
            $item->advertising_cost = $manualCosts[$item->date]->advertising_cost ?? 0;
            $item->other_cost = $manualCosts[$item->date]->other_cost ?? 0;                         

            return $item;
        });

        // Total semua total_tf dan total_cod
        // $total_tf = $newData->sum(function($item) {
        //     return (float) $item->total_profit_tf;
        // });

        // $total_cod = $newData->sum(function($item) {
        //     return (float) $item->total_profit_cod;
        // });

        // // Hasil akhir
        // return $summary = [
        //     'total_tf' => $total_tf,
        //     'total_cod' => $total_cod,
        //     'data' => $newData,
        //     'tf_cod' => $total_tf + $total_cod,
        // ];

        // return $newData;        
        return DataTables::of($newData)
            ->addIndexColumn()
            ->addColumn('cpr', function ($newData) {                                
                $close_tf = $newData->close_order_tf ?? 0;
                $close_cod = $newData->close_order_cod ?? 0;
                $total_close = $close_tf + $close_cod;

                if ($total_close == 0) {
                    return 0; 
                }

                return $newData->advertising_cost / $total_close;
            })
            ->rawColumns(['cpr'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.earning-potential.modal', [
            'title' => 'Earning Potential',
            'isEditing' => false,
            'advertisers' => User::where('level_user', 4)->orderBy('name', 'asc')->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $request;
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request)
    {        
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // $data = $this->biayaIklanService->show($id);
        return view('data-master.earning-potential.modal', [
            'title' => 'Earning Potential',
            'earningPotential' => null,
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {        
        $validated = $request->validate([
            'date' => 'required|date',
            'handled_by' => 'required|exists:users,id',
            'field' => 'required|in:advertising_cost,other_cost',
            'value' => 'required|integer|min:0'
        ]);

        //throw jika bukan level 1 yang update
        if (auth()->user()->level_user != 1) {
            throw new \Exception('Hanya admin yang dapat mengubah data ini');
        }

        EarningPotential::updateOrCreate(
            [
                'user_id' => $validated['handled_by'],
                'date' => $validated['date']
            ],
            [
                $validated['field'] => (int) $validated['value'],
                'updated_at' => now()
            ]
        );

        return response()->json(['success' => true]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
    public function getData(Request $request){        

        $filter = [];

        if ($request->has('adv_id')) {
            $filter['adv_id'] = $request->get('adv_id');
        }

        if ($request->has('bulan')) {
            $bulanSearch = $request->get('bulan');
        }                
        
        $data = $this->earningPotentialService->summary($filter['adv_id'] ?? null, $bulanSearch ?? null);
        return response()->json($data);
    }
}
