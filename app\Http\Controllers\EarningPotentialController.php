<?php

namespace App\Http\Controllers;

use App\Enums\LevelUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\EarningPotential\EarningPotentialService;

class EarningPotentialController extends Controller
{
    public function __construct(protected EarningPotentialService $earningPotentialService)
    {
    }

    /**
     * <PERSON><PERSON>lan pagawai.
     */
    public function index()
    {
        return view('earning-potential.index', [
            'title' => 'Earning Potential'
        ]);
    }

    public function data()
    {
        $data = $this->earningPotentialService->grid();
        return DataTables::of($data)
            ->addIndexColumn()
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
