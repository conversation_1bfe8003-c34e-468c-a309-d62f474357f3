<?php

namespace App\Http\Controllers;

use App\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiKeyController extends Controller
{
    /**
     * Display the API key configuration page.
     */
    public function index()
    {
        $apiKey = ApiKey::getConfig();

        return view('api-keys.index', [
            'title' => 'API Key Configuration',
            'apiKey' => $apiKey
        ]);
    }

    /**
     * Update the API key configuration.
     */
    public function update(Request $request)
    {
        // Validasi input
        $validator = Validator::make($request->all(), [
            'rajaongkir_endpoint' => 'nullable|url',
            'rajaongkir_api_key' => 'nullable|string',
            'kiriminaja_env' => 'required|in:dev,prod',
            'kiriminaja_dev_base_url' => 'nullable|url',
            'kiriminaja_dev_api_key' => 'nullable|string',
            'kiriminaja_prod_base_url' => 'nullable|url',
            'kiriminaja_prod_api_key' => 'nullable|string',
            'facebook_app_id' => 'nullable|string',
            'facebook_app_secret' => 'nullable|string',
            'facebook_access_token' => 'nullable|string',
            'woowa_phone_num' => 'nullable|string',
            'woowa_base_url' => 'nullable|url',
            'woowa_api_key' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation Error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Update atau create konfigurasi API Key
            ApiKey::updateConfig($request->all());

            return response()->json([
                'success' => true,
                'message' => 'API Key configuration updated successfully!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating API Key configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request)
    {
        $service = $request->input('service');

        \Log::info("Testing connection for service: {$service}");

        try {
            // Temporarily update configuration with current form data for testing
            $currentConfig = $request->except(['service', '_token', '_method']);
            if (!empty($currentConfig)) {
                // Store current form data temporarily for this test
                \Log::info("Using form data for test", $currentConfig);
            }

            switch ($service) {
                case 'rajaongkir':
                    $serviceInstance = app(\App\Services\RajaOngkir\RajaOngkirService::class);

                    // Check configuration from form data or database
                    $apiKey = $request->input('rajaongkir_api_key') ?: optional(ApiKey::first())->rajaongkir_api_key;
                    $endpoint = $request->input('rajaongkir_endpoint') ?: optional(ApiKey::first())->rajaongkir_endpoint;

                    if (empty($apiKey)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'RajaOngkir API key is required for testing'
                        ]);
                    }

                    // Create a temporary service instance with form data
                    $result = $this->testRajaOngkirConnection($apiKey, $endpoint);
                    break;

                case 'kiriminaja':
                    $env = $request->input('kiriminaja_env') ?: optional(ApiKey::first())->kiriminaja_env;

                    if ($env === 'prod') {
                        $apiKey = $request->input('kiriminaja_prod_api_key') ?: optional(ApiKey::first())->kiriminaja_prod_api_key;
                        $baseUrl = $request->input('kiriminaja_prod_base_url') ?: optional(ApiKey::first())->kiriminaja_prod_base_url;
                    } else {
                        $apiKey = $request->input('kiriminaja_dev_api_key') ?: optional(ApiKey::first())->kiriminaja_dev_api_key;
                        $baseUrl = $request->input('kiriminaja_dev_base_url') ?: optional(ApiKey::first())->kiriminaja_dev_base_url;
                    }

                    if (empty($apiKey)) {
                        return response()->json([
                            'success' => false,
                            'message' => "KiriminAja {$env} API key is required for testing"
                        ]);
                    }

                    $result = $this->testKiriminAjaConnection($apiKey, $baseUrl, $env);
                    break;

                case 'facebook':
                    $appId = $request->input('facebook_app_id') ?: optional(ApiKey::first())->facebook_app_id;
                    $appSecret = $request->input('facebook_app_secret') ?: optional(ApiKey::first())->facebook_app_secret;
                    $accessToken = $request->input('facebook_access_token') ?: optional(ApiKey::first())->facebook_access_token;

                    if (empty($appId) || empty($appSecret) || empty($accessToken)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Facebook App ID, App Secret, and Access Token are required for testing'
                        ]);
                    }

                    $result = $this->testFacebookConnection($appId, $appSecret, $accessToken);
                    break;

                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Service not supported: ' . $service
                    ], 400);
            }

            \Log::info("Test connection result for {$service}", $result);

            return response()->json($result);

        } catch (\Exception $e) {
            \Log::error("API connection test error for {$service}: " . $e->getMessage(), [
                'service' => $service,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'debug_info' => [
                    'service' => $service,
                    'error_type' => get_class($e)
                ]
            ], 500);
        }
    }

    /**
     * Test RajaOngkir connection with specific credentials
     */
    private function testRajaOngkirConnection($apiKey, $endpoint = null)
    {
        try {
            $client = new \GuzzleHttp\Client();
            $baseUrl = $endpoint ?: 'https://pro.rajaongkir.com';

            // Test with specific province ID as recommended in API docs
            $response = $client->get($baseUrl . '/api/province', [
                'query' => [
                    'id' => '12' // Test with Kalimantan Barat
                ],
                'headers' => [
                    'key' => $apiKey
                ],
                'timeout' => 10
            ]);

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody(), true);

                // Check if response has proper RajaOngkir structure
                if (!isset($data['rajaongkir'])) {
                    return [
                        'success' => false,
                        'message' => 'Invalid RajaOngkir API response format'
                    ];
                }

                $rajaOngkirStatus = $data['rajaongkir']['status'] ?? null;

                if (!$rajaOngkirStatus) {
                    return [
                        'success' => false,
                        'message' => 'Missing status in RajaOngkir response'
                    ];
                }

                $statusCode = $rajaOngkirStatus['code'] ?? null;
                $statusDescription = $rajaOngkirStatus['description'] ?? 'Unknown error';

                // Check RajaOngkir specific status codes
                switch ($statusCode) {
                    case 200:
                        // Success - check if we got the expected province data
                        $results = $data['rajaongkir']['results'] ?? null;
                        if ($results && isset($results['province_id']) && $results['province_id'] == '12') {
                            return [
                                'success' => true,
                                'message' => 'RajaOngkir connection successful',
                                'data' => [
                                    'province' => $results['province'] ?? 'Kalimantan Barat',
                                    'endpoint' => $baseUrl
                                ]
                            ];
                        } else {
                            return [
                                'success' => false,
                                'message' => 'RajaOngkir API response incomplete or invalid'
                            ];
                        }

                    case 400:
                        return [
                            'success' => false,
                            'message' => 'Invalid RajaOngkir API key: ' . $statusDescription
                        ];

                    case 401:
                        return [
                            'success' => false,
                            'message' => 'Unauthorized RajaOngkir API access: ' . $statusDescription
                        ];

                    case 403:
                        return [
                            'success' => false,
                            'message' => 'RajaOngkir API access forbidden: ' . $statusDescription
                        ];

                    case 429:
                        return [
                            'success' => false,
                            'message' => 'RajaOngkir API rate limit exceeded: ' . $statusDescription
                        ];

                    default:
                        return [
                            'success' => false,
                            'message' => "RajaOngkir API error (Code: {$statusCode}): {$statusDescription}"
                        ];
                }
            }

            return [
                'success' => false,
                'message' => 'Unexpected HTTP response code: ' . $response->getStatusCode()
            ];

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();

            // Try to get RajaOngkir error message from response body
            try {
                $errorBody = json_decode($e->getResponse()->getBody(), true);
                if (isset($errorBody['rajaongkir']['status']['description'])) {
                    $errorMessage = $errorBody['rajaongkir']['status']['description'];
                } else {
                    $errorMessage = 'HTTP ' . $statusCode . ' error';
                }
            } catch (\Exception $jsonError) {
                $errorMessage = 'HTTP ' . $statusCode . ' error';
            }

            $specificMessages = [
                400 => 'Bad Request - Check your API key format',
                401 => 'Unauthorized - Invalid API key',
                403 => 'Forbidden - API key lacks sufficient permissions',
                404 => 'Not Found - Check the endpoint URL',
                429 => 'Too Many Requests - Rate limit exceeded'
            ];

            $message = $specificMessages[$statusCode] ?? $errorMessage;

            return [
                'success' => false,
                'message' => 'RajaOngkir API error: ' . $message
            ];

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            return [
                'success' => false,
                'message' => 'Cannot connect to RajaOngkir server. Please check your internet connection or endpoint URL.'
            ];

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return [
                'success' => false,
                'message' => 'RajaOngkir request failed: ' . $e->getMessage()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'RajaOngkir connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test KiriminAja connection with specific credentials
     */
    private function testKiriminAjaConnection($apiKey, $baseUrl, $env)
    {
        try {
            $client = new \GuzzleHttp\Client();
            $url = $baseUrl ?: ($env === 'prod' ? 'https://api.kiriminaja.com' : 'https://tdev.kiriminaja.com');

            // Test with the correct KiriminAja endpoint using POST method
            $response = $client->post($url . '/api/mitra/province', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $apiKey,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 10
            ]);

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody(), true);

                // Check KiriminAja response structure
                if (!is_array($data)) {
                    return [
                        'success' => false,
                        'message' => "Invalid KiriminAja {$env} API response format"
                    ];
                }

                // Check for KiriminAja specific error responses
                if (isset($data['status']) && $data['status'] === false) {
                    $errorText = $data['text'] ?? 'Unknown error';
                    $yourIp = $data['your_ip'] ?? 'Unknown';

                    // Handle IP restriction specifically
                    if (strpos($errorText, 'IP address') !== false && strpos($errorText, 'not allowed') !== false) {
                        return [
                            'success' => false,
                            'message' => "KiriminAja {$env} IP restriction: {$errorText} (Your IP: {$yourIp})"
                        ];
                    }

                    return [
                        'success' => false,
                        'message' => "KiriminAja {$env} API error: {$errorText}"
                    ];
                }

                // Check for successful response (typically an array of provinces or success status)
                if (isset($data['status']) && $data['status'] === true) {
                    return [
                        'success' => true,
                        'message' => "KiriminAja {$env} connection successful",
                        'data' => [
                            'environment' => $env,
                            'endpoint' => $url,
                            'response_type' => 'success_status'
                        ]
                    ];
                }

                // If response is an array of provinces (successful data response)
                if (is_array($data) && !isset($data['status'])) {
                    return [
                        'success' => true,
                        'message' => "KiriminAja {$env} connection successful",
                        'data' => [
                            'environment' => $env,
                            'endpoint' => $url,
                            'provinces_count' => count($data),
                            'response_type' => 'province_data'
                        ]
                    ];
                }

                return [
                    'success' => false,
                    'message' => "KiriminAja {$env} unexpected response format"
                ];
            }

            return [
                'success' => false,
                'message' => "KiriminAja {$env} unexpected HTTP response code: " . $response->getStatusCode()
            ];

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();

            // Try to get KiriminAja error message from response body
            try {
                $errorBody = json_decode($e->getResponse()->getBody(), true);
                if (isset($errorBody['text'])) {
                    $errorMessage = $errorBody['text'];

                    // Handle IP restriction in error response
                    if (strpos($errorMessage, 'IP address') !== false && strpos($errorMessage, 'not allowed') !== false) {
                        $yourIp = $errorBody['your_ip'] ?? 'Unknown';
                        return [
                            'success' => false,
                            'message' => "KiriminAja {$env} IP restriction: {$errorMessage} (Your IP: {$yourIp})"
                        ];
                    }

                    return [
                        'success' => false,
                        'message' => "KiriminAja {$env} API error: {$errorMessage}"
                    ];
                }
            } catch (\Exception $jsonError) {
                // Continue with standard error handling
            }

            $specificMessages = [
                400 => 'Bad Request - Check your API key format or request data',
                401 => 'Unauthorized - Invalid API key',
                403 => 'Forbidden - API key lacks sufficient permissions or IP not whitelisted',
                404 => 'Not Found - Check the endpoint URL',
                422 => 'Unprocessable Entity - Invalid request data',
                429 => 'Too Many Requests - Rate limit exceeded',
                500 => 'Internal Server Error - KiriminAja server error'
            ];

            $message = $specificMessages[$statusCode] ?? "HTTP {$statusCode} error";

            return [
                'success' => false,
                'message' => "KiriminAja {$env} API error: {$message}"
            ];

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            return [
                'success' => false,
                'message' => "Cannot connect to KiriminAja {$env} server. Please check your internet connection or endpoint URL."
            ];

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return [
                'success' => false,
                'message' => "KiriminAja {$env} request failed: " . $e->getMessage()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => "KiriminAja {$env} connection test failed: " . $e->getMessage()
            ];
        }
    }

    /**
     * Test Facebook connection with specific credentials
     */
    private function testFacebookConnection($appId, $appSecret, $accessToken)
    {
        try {
            $client = new \GuzzleHttp\Client();

            // Generate App Access Token for debug_token endpoint
            $appToken = $appId . '|' . $appSecret;

            // Use debug_token endpoint for comprehensive token validation
            $response = $client->get('https://graph.facebook.com/debug_token', [
                'query' => [
                    'input_token' => $accessToken,
                    'access_token' => $appToken
                ],
                'timeout' => 10
            ]);

            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody(), true);

                // Check if response has proper Facebook structure
                if (!isset($data['data'])) {
                    return [
                        'success' => false,
                        'message' => 'Invalid Facebook API response format'
                    ];
                }

                $tokenData = $data['data'];

                // Check if token is valid
                if (isset($tokenData['is_valid']) && $tokenData['is_valid'] === true) {
                    // Token is valid, extract useful information
                    $expiresAt = isset($tokenData['expires_at']) ?
                        date('Y-m-d H:i:s', $tokenData['expires_at']) : 'Never expires';

                    $scopes = $tokenData['scopes'] ?? [];
                    $scopesList = !empty($scopes) ? implode(', ', $scopes) : 'No specific scopes';

                    return [
                        'success' => true,
                        'message' => 'Facebook connection successful',
                        'data' => [
                            'app_id' => $tokenData['app_id'] ?? 'Unknown',
                            'user_id' => $tokenData['user_id'] ?? 'Unknown',
                            'expires_at' => $expiresAt,
                            'scopes' => $scopesList,
                            'scopes_count' => count($scopes),
                            'token_type' => $tokenData['type'] ?? 'Unknown',
                            'application' => $tokenData['application'] ?? 'Unknown'
                        ]
                    ];
                } else {
                    // Token is invalid, check for error details
                    if (isset($tokenData['error'])) {
                        $errorInfo = $tokenData['error'];
                        $errorMessage = $errorInfo['message'] ?? 'Invalid access token';
                        $errorCode = $errorInfo['code'] ?? 'Unknown';

                        return [
                            'success' => false,
                            'message' => "Facebook token validation failed: {$errorMessage} (Code: {$errorCode})"
                        ];
                    }

                    return [
                        'success' => false,
                        'message' => 'Facebook access token is invalid or expired'
                    ];
                }
            }

            return [
                'success' => false,
                'message' => 'Unexpected HTTP response code: ' . $response->getStatusCode()
            ];

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $statusCode = $e->getResponse()->getStatusCode();

            // Try to get Facebook error message from response body
            try {
                $errorBody = json_decode($e->getResponse()->getBody(), true);
                if (isset($errorBody['error'])) {
                    $error = $errorBody['error'];
                    $errorMessage = $error['message'] ?? 'Unknown error';
                    $errorType = $error['type'] ?? 'Unknown';
                    $errorCode = $error['code'] ?? $statusCode;

                    // Handle specific Facebook error types
                    switch ($errorType) {
                        case 'OAuthException':
                            return [
                                'success' => false,
                                'message' => "Facebook OAuth error: {$errorMessage}"
                            ];
                        case 'GraphMethodException':
                            return [
                                'success' => false,
                                'message' => "Facebook API method error: {$errorMessage}"
                            ];
                        default:
                            return [
                                'success' => false,
                                'message' => "Facebook API error ({$errorType}): {$errorMessage}"
                            ];
                    }
                }
            } catch (\Exception $jsonError) {
                // Continue with standard error handling
            }

            $specificMessages = [
                400 => 'Bad Request - Check your App ID, App Secret, or Access Token format',
                401 => 'Unauthorized - Invalid App ID or App Secret',
                403 => 'Forbidden - App lacks sufficient permissions',
                404 => 'Not Found - Check the API endpoint',
                429 => 'Too Many Requests - Rate limit exceeded',
                500 => 'Internal Server Error - Facebook server error'
            ];

            $message = $specificMessages[$statusCode] ?? "HTTP {$statusCode} error";

            return [
                'success' => false,
                'message' => "Facebook API error: {$message}"
            ];

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            return [
                'success' => false,
                'message' => 'Cannot connect to Facebook Graph API. Please check your internet connection.'
            ];

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            return [
                'success' => false,
                'message' => 'Facebook request failed: ' . $e->getMessage()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Facebook connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Refresh configuration cache for all services
     */
    public function refreshConfig()
    {
        try {
            // Clear all API configuration caches
            \Cache::forget('rajaongkir_config');
            \Cache::forget('kiriminaja_config');
            \Cache::forget('facebook_config');

            // Refresh configurations in services
            $rajaOngkir = app(\App\Services\RajaOngkir\RajaOngkirService::class);
            $rajaOngkir->refreshConfig();

            $kiriminAja = app(\App\Services\KiriminAja\KiriminAjaBaseService::class);
            $kiriminAja->refreshConfig();

            $facebook = app(\App\Services\Facebook\OptimizedFacebookAdService::class);
            $facebook->refreshConfig();

            return response()->json([
                'success' => true,
                'message' => 'Configuration cache refreshed successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Configuration refresh error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Debug configuration status
     */
    public function debugConfig()
    {
        try {
            $apiKey = ApiKey::first();

            if (!$apiKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'No API key configuration found in database'
                ]);
            }

            $config_status = [
                'rajaongkir' => [
                    'endpoint' => !empty($apiKey->rajaongkir_endpoint),
                    'api_key' => !empty($apiKey->rajaongkir_api_key),
                    'endpoint_value' => $apiKey->rajaongkir_endpoint,
                    'api_key_length' => strlen($apiKey->rajaongkir_api_key ?? '')
                ],
                'kiriminaja' => [
                    'env' => !empty($apiKey->kiriminaja_env),
                    'dev_base_url' => !empty($apiKey->kiriminaja_dev_base_url),
                    'dev_api_key' => !empty($apiKey->kiriminaja_dev_api_key),
                    'prod_base_url' => !empty($apiKey->kiriminaja_prod_base_url),
                    'prod_api_key' => !empty($apiKey->kiriminaja_prod_api_key),
                    'env_value' => $apiKey->kiriminaja_env,
                    'dev_base_url_value' => $apiKey->kiriminaja_dev_base_url,
                    'prod_base_url_value' => $apiKey->kiriminaja_prod_base_url
                ],
                'facebook' => [
                    'app_id' => !empty($apiKey->facebook_app_id),
                    'app_secret' => !empty($apiKey->facebook_app_secret),
                    'access_token' => !empty($apiKey->facebook_access_token),
                    'app_id_length' => strlen($apiKey->facebook_app_id ?? ''),
                    'app_secret_length' => strlen($apiKey->facebook_app_secret ?? ''),
                    'access_token_length' => strlen($apiKey->facebook_access_token ?? '')
                ],
                'woowa' => [
                    'phone_num' => !empty($apiKey->woowa_phone_num),
                    'base_url' => !empty($apiKey->woowa_base_url),
                    'api_key' => !empty($apiKey->woowa_api_key),
                    'base_url_value' => $apiKey->woowa_base_url
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'Configuration status retrieved',
                'data' => $config_status
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving configuration: ' . $e->getMessage()
            ], 500);
        }
    }
}
