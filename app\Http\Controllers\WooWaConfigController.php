<?php

namespace App\Http\Controllers;

use App\Models\CsWoowaConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class WooWaConfigController extends Controller
{
    /**
     * Display the WooWa API key configuration page for CS users.
     */
    public function index()
    {
        // Check if user is CS role
        if (Auth::user()->level_user !== 3) {
            abort(403, 'Unauthorized access');
        }

        $config = CsWoowaConfiguration::getConfigForCurrentUser();

        return view('woowa-config.index', [
            'title' => 'Konfigurasi WooWa Integration',
            'config' => $config,
            'user' => Auth::user()
        ]);
    }

    /**
     * Update the WooWa API key configuration.
     */
    public function update(Request $request)
    {
        // Check if user is CS role
        if (Auth::user()->level_user !== 3) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        // Validasi input
        $validator = Validator::make($request->all(), [
            'woowa_phone_num' => 'required|string|regex:/^[0-9+]+$/|min:10|max:20',
            'woowa_base_url' => 'required|url',
            'woowa_api_key' => 'required|string|min:10',
            'is_active' => 'boolean',
        ], [
            'woowa_phone_num.required' => 'Nomor WhatsApp harus diisi',
            'woowa_phone_num.regex' => 'Nomor WhatsApp hanya boleh berisi angka dan tanda +',
            'woowa_phone_num.min' => 'Nomor WhatsApp minimal 10 digit',
            'woowa_phone_num.max' => 'Nomor WhatsApp maksimal 20 digit',
            'woowa_base_url.required' => 'Base URL harus diisi',
            'woowa_base_url.url' => 'Base URL harus berformat URL yang valid',
            'woowa_api_key.required' => 'API Key harus diisi',
            'woowa_api_key.min' => 'API Key minimal 10 karakter',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation Error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Get or create configuration for current user
            $config = CsWoowaConfiguration::getConfigForCurrentUser();

            $updateData = [
                'user_id' => Auth::id(),
                'woowa_phone_num' => $request->input('woowa_phone_num'),
                'woowa_base_url' => rtrim($request->input('woowa_base_url'), '/'),
                'woowa_api_key' => $request->input('woowa_api_key'),
                'is_active' => $request->input('is_active', true),
            ];

            if ($config->exists) {
                $config->update($updateData);
                $message = 'Konfigurasi WooWa berhasil diperbarui!';
            } else {
                $config = CsWoowaConfiguration::create($updateData);
                $message = 'Konfigurasi WooWa berhasil dibuat!';
            }

            Log::info('WooWa configuration updated', [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name,
                'config_id' => $config->id,
            ]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'config_id' => $config->id
            ]);

        } catch (\Exception $e) {
            Log::error('Error updating WooWa configuration', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Error updating WooWa configuration: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test WooWa connection
     */
    public function testConnection(Request $request)
    {
        // Check if user is CS role
        if (Auth::user()->level_user !== 3) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        try {
            // Get form data or existing config
            $phoneNum = $request->input('woowa_phone_num');
            $baseUrl = $request->input('woowa_base_url');
            $apiKey = $request->input('woowa_api_key');

            // If no form data, get from existing config
            if (empty($phoneNum) || empty($baseUrl) || empty($apiKey)) {
                $config = CsWoowaConfiguration::getConfigForCurrentUser();
                $phoneNum = $phoneNum ?: $config->woowa_phone_num;
                $baseUrl = $baseUrl ?: $config->woowa_base_url;
                $apiKey = $apiKey ?: $config->woowa_api_key;
            }

            if (empty($apiKey)) {
                return response()->json([
                    'success' => false,
                    'message' => 'WooWa API key is required for testing'
                ]);
            }

            if (empty($phoneNum)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Phone number is required for testing'
                ]);
            }

            if (empty($baseUrl)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Base URL is required for testing'
                ]);
            }

            // Test WooWa connection
            $result = $this->testWooWaConnection($apiKey, $baseUrl, $phoneNum);

            // Update test result in database
            $config = CsWoowaConfiguration::getConfigForCurrentUser();
            if ($config->exists) {
                $config->updateTestResult($result, $result['success']);
            }

            Log::info('WooWa connection test performed', [
                'user_id' => Auth::id(),
                'user_name' => Auth::user()->name,
                'success' => $result['success'],
                'phone_num' => $phoneNum,
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('WooWa connection test failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test connection failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test WooWa API connection
     */
    private function testWooWaConnection($apiKey, $baseUrl, $phoneNum): array
    {
        try {
            $testMessage = "Test koneksi WooWa API dari CS: " . Auth::user()->name . " - " . date('Y-m-d H:i:s');

            $data = [
                "phone_no" => $phoneNum,
                "key" => $apiKey,
                "message" => $testMessage
            ];

            $data_string = json_encode($data, JSON_UNESCAPED_UNICODE);
            $url = rtrim($baseUrl, '/') . '/send_message';

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_VERBOSE, 0);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                return [
                    'success' => false,
                    'message' => 'Connection error: ' . $curlError
                ];
            }

            if ($httpCode === 200) {
                $responseData = json_decode($response, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($responseData)) {
                    return [
                        'success' => true,
                        'message' => 'WooWa connection successful! Test message sent.',
                        'data' => $responseData,
                        'http_code' => $httpCode
                    ];
                } else {
                    return [
                        'success' => true,
                        'message' => 'WooWa connection successful (HTTP 200), but response format is unexpected',
                        'raw_response' => $response,
                        'http_code' => $httpCode
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => "WooWa API returned HTTP {$httpCode}",
                    'response' => $response,
                    'http_code' => $httpCode
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get configuration status for current user
     */
    public function status()
    {
        // Check if user is CS role
        if (Auth::user()->level_user !== 3) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access'
            ], 403);
        }

        try {
            $config = CsWoowaConfiguration::getConfigForCurrentUser();

            return response()->json([
                'success' => true,
                'data' => [
                    'is_configured' => $config->exists && $config->isComplete(),
                    'is_active' => $config->is_active ?? false,
                    'last_tested_at' => $config->last_tested_at?->format('Y-m-d H:i:s'),
                    'last_test_successful' => $config->isLastTestSuccessful(),
                    'phone_formatted' => $config->formatted_phone ?? '-',
                    'status_badge' => $config->status_badge ?? '<span class="badge bg-warning">Belum Dikonfigurasi</span>',
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting status: ' . $e->getMessage()
            ], 500);
        }
    }
}
