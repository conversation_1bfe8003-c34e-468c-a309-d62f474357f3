<?php

namespace App\Http\Controllers\Laporan;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Models\InquiryOrderKiriminAja;
use Illuminate\Support\Facades\Log;
use App\Services\Order\OrderService;

class LaporanPengirimanController extends Controller
{
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * Display the shipping report index page
     */
    public function index()
    {
        return view('laporan.pengiriman.index', [
            'title' => 'Laporan Pengiriman'
        ]);
    }

    /**
     * Get shipping data for DataTables
     */
    public function data(Request $request)
    {
        try {
            $query = InquiryOrderKiriminAja::query();

            // Apply date filter if provided
            if ($request->has('date') && !empty($request->date)) {
                try {
                    [$startDate, $endDate] = explode(' to ', $request->date);
                    $query->whereBetween('schedule', [
                        Carbon::parse($startDate)->startOfDay(),
                        Carbon::parse($endDate)->endOfDay()
                    ]);
                } catch (\Exception $e) {
                    Log::warning('Date parsing error in LaporanPengirimanController: ' . $e->getMessage());
                    // If date parsing fails, get current month data
                    $query->whereBetween('schedule', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                }
            } else {
                // Default to current month if no date provided
                $query->whereBetween('schedule', [
                    Carbon::now()->startOfMonth(),
                    Carbon::now()->endOfMonth()
                ]);
            }
            $query->orderBy('schedule', 'desc');

            return DataTables::of($query)
                ->addIndexColumn()
                ->addColumn('no_resi', function ($row) {
                    try {
                        $packages = $row->packages;
                        $orderId = $packages[0]['order_id'] ?? '-';
                        if ($orderId !== '-') {
                            $orderId = str_replace('JUAL-', '', $orderId);
                            $order = $this->orderService->findByOrderId($orderId);
                            return $order->receipt_number ?? '-';
                        }
                        return '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing no_resi for row ' . $row->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('id_order', function ($row) {
                    try {
                        $packages = $row->packages;
                        return $packages[0]['order_id'] ?? '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing id_order for row ' . $row->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('qty', function ($row) {
                    try {
                        return is_array($row->packages) ? count($row->packages) : 1;
                    } catch (\Exception $e) {
                        Log::warning('Error parsing qty for row ' . $row->id . ': ' . $e->getMessage());
                        return 1;
                    }
                })
                ->addColumn('tanggal_kirim', function ($row) {
                    try {
                        return $row->schedule ? Carbon::parse($row->schedule)->format('d-m-Y') : '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing tanggal_kirim for row ' . $row->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('nama_penerima', function ($row) {
                    try {
                        $packages = $row->packages;
                        return $packages[0]['destination_name'] ?? '-';
                    } catch (\Exception $e) {
                        Log::warning('Error parsing nama_penerima for row ' . $row->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->addColumn('ekspedisi', function ($row) {
                    try {
                        $packages = $row->packages;
                        return strtoupper($packages[0]['service'] ?? '-');
                    } catch (\Exception $e) {
                        Log::warning('Error parsing ekspedisi for row ' . $row->id . ': ' . $e->getMessage());
                        return '-';
                    }
                })
                ->rawColumns(['no_resi'])
                ->make(true);

        } catch (\Exception $e) {
            Log::error('Error in LaporanPengirimanController@data: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            return response()->json([
                'error' => 'Terjadi kesalahan saat mengambil data',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
