<?php

namespace App\Http\Controllers\Laporan;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\StokRetur\StokReturService;

class LaporanBarangRusakController extends Controller
{
    public function __construct(protected StokReturService $stokReturService)
    {
    }

    /**
     * Tampilan laporan barang rusak.
     */
    public function index()
    {
        return view('laporan.barang-rusak.index', [
            'title' => 'Laporan Barang Rusak',
        ]);
    }

    public function data()
    {
        $data = $this->stokReturService->getAllWithRetur('tidak_layak');

        return DataTables::of($data)
            ->addIndexColumn()
            ->editColumn('nama_produk', function ($row) {
                return $row->retur->nama_produk ?? 'Produk Tidak Ditemukan';
            })
            ->editColumn('kode_produk', function ($row) {
                return $row->retur->kode_produk ?? 'Kode Produk Tidak Ditemukan';
            })
            ->editColumn('ads_team_id', function ($row) {
                return 'N/A';
            })
            ->editColumn('jumlah', function ($row) {
                return $row->jumlah_retur ?? '0';
            })
            ->addColumn('catatan', function ($row) {
                return $row->catatan ?? '-';
            })
            ->rawColumns(['catatan'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
