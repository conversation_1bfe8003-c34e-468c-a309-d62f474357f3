<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;

class OrderCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The data associated with the order.
     *
     * @var object An object containing at least 'created_at' and 'title' properties.
     */
    public $data;
    /**
     * Create a new event instance.
     */
    public function __construct($data)
    {
        if (!is_object($data) || !property_exists($data, 'created_at') || !property_exists($data, 'title')) {
            throw new \InvalidArgumentException('The $data parameter must be an object with properties "created_at" and "title".');
        }
        $this->data = $data;
    }
    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('orders');
    }

    /**
     * Get the event name for broadcasting.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'order.created';
    }

    /**
     * Get the data to broadcast with the event.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        static $cachedMessage = null;

        if ($cachedMessage === null) {
            $cachedMessage = "{$this->data->title}.";
        }

        return [
            'message' => $cachedMessage,
            'created_at' => $this->data->created_at,
            'title' => 'New Order #' . $this->data->order_id,
        ];
    }
}
