<?php

namespace App\Http\Controllers;

use App\Models\Retur;
use App\Models\StokRetur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Services\Retur\ReturService;
use Yajra\DataTables\Facades\DataTables;
use App\Services\Laporan\ListKulakService;
use App\Services\StokRetur\StokReturService;

class StokReturController extends Controller
{
    public function __construct(protected StokReturService $stokreturService, protected ReturService $returService)
    {
    }

    /**
     * <PERSON><PERSON>lan pagawai.
     */
    public function index()
    {
        return view('stok-retur.index', [
            'title' => 'Stok Retur'
        ]);
    }

    public function data()
    {
        $filters = [];

        if (request()->has('status')) {
            $filters['status'] = request()->get('status', 'pending');
        }

        if (request()->has('date')) {
            $date = request()->get('date');
            $dates = explode(' to ', $date);
            $filters['start_date'] = $dates[0];
            $filters['end_date'] = $dates[1] ?? $dates[0];
        }

        $data = $this->returService->getByFilter($filters);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('checkbox', function ($row) {
                return '<div class="form-check">
                    <input class="form-check-input row-checkbox" type="checkbox"
                           value="' . $row->id . '" data-status="' . $row->status . '">
                </div>';
            })
            ->editColumn('returned_at', fn($row) => $row->tanggal_retur?->format('d-m-Y H:i:s') ?? '-')
            ->addColumn('product', fn($row) => $row->nama_produk ?? 'Produk Tidak Ditemukan')
            ->addColumn('customer_name', fn($row) => $row->nama_pelanggan ?? 'Nama Tidak Tersedia')
            ->addColumn('customer_phone', fn($row) => $row->telepon_pelanggan ?? 'Nomor Tidak Tersedia')
            ->addColumn('quantity', fn($row) => $row->jumlah ?? 0)
            ->addColumn('ads_team_id', fn($row) => 'N/A')
            ->addColumn('status', fn($row) => view('components.kelayakan-badge', ['status' => $row->status]))
            ->addColumn('checked_by', fn($row) => view('components.checked-by', ['name' => $row->diperiksa_oleh, 'date' => $row->diperiksa_pada]))
            ->addColumn('action', fn($row) => view('components.kelayakan-dropdown', [
                'id' => $row->id,
                'status' => $row->status // Kirim status saat ini ke komponen
            ]))
            ->rawColumns(['checkbox', 'action', 'status'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:layak,tidak_layak,pending',
            'notes' => 'required_if:status,tidak_layak|min:10|max:255|nullable'
        ]);

        // Cari data retur
        $retur = $this->returService->find($id, ['stokRetur']);

        if (!$retur) {
            throw new \Exception('Data retur tidak ditemukan');
        }

        //update status di retur service
        $this->returService->updateStatus($id, $request->input('status'));

        if ($request->input('status') === 'pending') {
            // Reset logic - hapus record stokRetur jika ada
            //hapus diperiksa_oleh dan diperiksa_pada

            $retur->diperiksa_oleh = null;
            $retur->diperiksa_pada = null;
            $retur->save();

            // Delete stokRetur records
            $retur->stokRetur()->delete();

            // Delete related list_kulak records
            $listKulakService = app(ListKulakService::class);
            $listKulakService->deleteByReturId($id);

            return response()->json([
                'message' => 'Status berhasil direset ke Pending. Record stok terkait telah dihapus.'
            ]);
        }

        // jika layak insert ke laporanStok ke stokRetur service
        // jika tidak layak insert ke laporanBarangRusak dengan keterangan notes di stokRetur
        $retur->stokRetur()->create([
            'kondisi' => $request->input('status'),
            'jumlah_retur' => $retur->jumlah,
            'catatan' => $request->input('notes') ?? null
        ]);

        return response()->json([
            'message' => 'Status berhasil diubah'
        ]);
    }

    public function kirimUlang(Request $request)
    {
        $request->validate([
            'jumlah' => 'required|integer|min:1'
        ]);

        $stokRetur = $this->stokreturService->find($request->input('id'));

        if (!$stokRetur) {
            throw new \Exception('Data stok retur tidak ditemukan');
        }

        $jumlahKirim = $request->input('jumlah');
        $totalKirim = $stokRetur->jumlah_kirim_ulang + $jumlahKirim;

        if ($totalKirim > $stokRetur->jumlah_retur) {
            throw new \Exception('Jumlah kirim ulang melebihi stok retur yang tersedia');
        }

        $this->stokreturService->update([
            'jumlah_retur' => $stokRetur->jumlah_retur - $jumlahKirim,
            'jumlah_kirim_ulang' => $totalKirim
        ], $stokRetur->id);

        // Create list kulak record
        $listKulakData = [
            'tanggal_kulak' => now()->format('Y-m-d'),
            'nama_produk' => $stokRetur->retur->nama_produk,
            'varian_produk' => [$stokRetur->retur->variasi],
            'harga' => $stokRetur->retur->harga_produk,
            'harga_reseller' => '-',
            'qty_tf' => ($stokRetur->retur->metode_pembayaran == 'bank_transfer') ? $jumlahKirim : 0,
            'qty_cod' => ($stokRetur->retur->metode_pembayaran == 'cod') ? $jumlahKirim : 0,
            'total_qty' => $jumlahKirim,
            'total_kulak' => $stokRetur->retur->harga_produk * $jumlahKirim,
            'status' => 'stok_retur',
            'link_produk' => '-',
            'nama_kurir' => '-',
            'no_resi' => '-',
        ];

        $listKulakService = app(ListKulakService::class);
        $listKulakService->store($listKulakData);

        return response()->json([
            'message' => 'Barang berhasil dikirim ulang.',
        ]);
    }

    /**
     * Update status for multiple items at once
     */
    public function updateStatusBulk(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:retur,id',
            'status' => 'required|in:layak,tidak_layak',
            'notes' => 'required_if:status,tidak_layak|min:10|max:255|nullable'
        ]);

        $ids = $request->input('ids');
        $status = $request->input('status');
        $notes = $request->input('notes');

        $successCount = 0;
        $failedCount = 0;

        DB::beginTransaction();

        try {
            foreach ($ids as $id) {
                // Cari data retur
                $retur = $this->returService->find($id, ['stokRetur']);

                if (!$retur) {
                    $failedCount++;
                    continue;
                }

                // Update status di retur service
                $this->returService->updateStatus($id, $status);

                // Jika layak atau tidak layak, insert ke stokRetur
                $retur->stokRetur()->create([
                    'kondisi' => $status,
                    'jumlah_retur' => $retur->jumlah,
                    'catatan' => $notes ?? null
                ]);

                $successCount++;
            }

            DB::commit();

            return response()->json([
                'message' => "Berhasil mengupdate $successCount item" . ($failedCount > 0 ? ", $failedCount item gagal diupdate" : "")
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Terjadi kesalahan: ' . $e->getMessage()
            ], 500);
        }
    }

    public function resetStatusBulk(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:retur,id' // Pastikan tabel yang benar
        ]);

        DB::beginTransaction();
        try {
            $count = 0;
            $validIds = Retur::whereIn('id', $request->ids)
                ->where('status', '!=', 'pending')
                ->pluck('id');

            foreach ($validIds as $id) {
                Retur::where('id', $id)->update([
                    'status' => 'pending',
                    'diperiksa_oleh' => null,
                    'diperiksa_pada' => null,
                ]);
                $count++;
            }

            DB::commit();

            return response()->json([
                'message' => "Berhasil mereset $count item",
                'invalid_ids' => array_diff($request->ids, $validIds->toArray())
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Gagal mereset status: ' . $e->getMessage()
            ], 500);
        }
    }
}
