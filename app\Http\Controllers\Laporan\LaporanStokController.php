<?php

namespace App\Http\Controllers\Laporan;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\StokRetur\StokReturService;

class LaporanStokController extends Controller
{
    public function __construct(protected StokReturService $stokReturService)
    {
    }

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('laporan.stok.index', [
            'title' => 'Laporan Stok',
        ]);
    }

    public function data()
    {
        $data = $this->stokReturService->getAllWithRetur('layak');

        return DataTables::of($data)
            ->addIndexColumn()
            ->editColumn('nama_produk', fn($row) => $row->retur->nama_produk ?? 'Produk Tidak Ditemukan')
            ->editColumn('kode_produk', fn($row) => $row->retur->kode_produk ?? 'Kode Produk Tidak Ditemukan')
            ->editColumn('ads_team_id', fn($row) => 'N/A')
            ->editColumn('stok_tersedia', fn($row) => $row->jumlah_retur ?? '0')
            ->editColumn('sisa_stok', fn($row) => $row->jumlah_kirim_ulang ?? '0')
            ->addColumn('action', function ($row) {
                return view('components.kirim-ulang-btn', ['id' => $row->id]);
            })
            ->rawColumns(['action'])
            ->make(true);

    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
