<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\BiayaAdv;
use App\Models\PaymentAdvMinusValue;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\Laporan\PaymentAdvService;

class PaymentADVController extends Controller
{

    public function __construct(protected PaymentAdvService $paymentAdvService)
    {
    }

    public function index()
    {
        return view('payment-adv.index', [
            'title' => 'Payment ADV',
            'advertisers' => User::where('level_user', 4)->orderBy('name', 'asc')->get(),
        ]);
    }

    public function data(Request $request)
    {
        $advId = null;
        $bulan = null;

        if ($request->has('advFilter') && !empty($request->get('advFilter'))) {
            $advId = $request->get('advFilter');
        }

        if ($request->has('bulanFilter') && !empty($request->get('bulanFilter'))) {
            $bulan = $request->get('bulanFilter');
        }

        // Ambil data biaya manual
        $manualCosts = BiayaAdv::select('date', 'advertising_cost', 'other_cost')
            //jika level 1 maka ambil advId dari request jika level 4 ambil dari auth
            ->when(auth()->user()->level_user == 1, fn($q) => $q->where('user_id', $advId))
            ->when(auth()->user()->level_user == 4, fn($q) => $q->where('user_id', auth()->user()->id))
            ->when($bulan, fn($q) => $q->whereBetween('date', [
                Carbon::parse($bulan)->startOfMonth(),
                Carbon::parse($bulan)->endOfMonth()
            ]))
            ->get()
            ->keyBy('date');

        $data = $this->paymentAdvService->grid($advId, $bulan);

        // gabungkan data
        $newData = $data->map(function ($item) use ($manualCosts) {
            $item->advertising_cost = $manualCosts[$item->date]->advertising_cost ?? 0;
            $item->other_cost = $manualCosts[$item->date]->other_cost ?? 0;
            return $item;
        });

        return DataTables::of($newData)
            ->addIndexColumn()
            ->make(true);
    }



    public function update(Request $request)
    {
        $validated = $request->validate([
            'date' => 'required|date',
            'adv_id' => 'required|exists:users,id',
            'field' => 'required|in:advertising_cost,other_cost',
            'value' => 'required|integer|min:0'
        ]);

        //throw jika bukan level 1 yang update
        if (auth()->user()->level_user != 1) {
            throw new \Exception('Hanya admin yang dapat mengubah data ini');
        }

        BiayaAdv::updateOrCreate(
            [
                'user_id' => $validated['adv_id'],
                'date' => $validated['date']
            ],
            [
                $validated['field'] => (int) $validated['value'],
                'updated_at' => now()
            ]
        );

        return response()->json(['success' => true]);
    }

    public function getMinusValues(Request $request)
    {
        $request->validate([
            'adv_id' => 'required|exists:users,id',
            'bulan' => 'required|date_format:Y-m'
        ]);

        $minusValues = PaymentAdvMinusValue::firstOrCreate(
            [
                'user_id' => $request->adv_id,
                //bulan sebelumnya
                'bulan' => date('Y-m-01', strtotime('-1 month', strtotime($request->bulan)))
            ],
            [
                'minus_last_month' => 0,
                'minus_cs_last_month' => 0
            ]
        );

        return response()->json([
            'success' => true,
            'minusLastMonth' => $minusValues->minus_last_month,
            'minusCsLastMonth' => $minusValues->minus_cs_last_month
        ]);
    }

    /**
     * Update minus values
     */
    public function updateMinus(Request $request)
    {
        // Only admin can update
        if (auth()->user()->level_user != 1) {
            return response()->json([
                'success' => false,
                'message' => 'Hanya admin yang dapat mengubah data ini'
            ], 403);
        }

        $validated = $request->validate([
            'adv_id' => 'required|exists:users,id',
            'bulan' => 'required|date_format:Y-m',
            'field' => 'required|in:minusLastMonth,minusCsLastMonth',
            'value' => 'required|integer|min:0'
        ]);

        $fieldMap = [
            'minusLastMonth' => 'minus_last_month',
            'minusCsLastMonth' => 'minus_cs_last_month'
        ];

        $minusValues = PaymentAdvMinusValue::updateOrCreate(
            [
                'user_id' => $validated['adv_id'],
                //bulan sebelumnya
                'bulan' => date('Y-m-01', strtotime('-1 month', strtotime($validated['bulan'])))
            ],
            [
                $fieldMap[$validated['field']] => $validated['value'],
                'updated_at' => now()
            ]
        );

        return response()->json([
            'success' => true,
            'updated' => $minusValues->wasChanged()
        ]);
    }
}
