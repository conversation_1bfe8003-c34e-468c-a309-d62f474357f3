<?php

namespace App\Http\Controllers\Laporan;

use App\Models\User;
use App\Enums\LevelUser;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;
use App\Services\Laporan\PaymentCSService;
use App\Http\Requests\DataMaster\PegawaiRequest;
use App\Models\Order;
use App\Models\Retur;
// use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\Auth;

class LaporanPaymentCSController extends Controller
{
    public function __construct(protected PaymentCSService $paymentCSService)
    {
    }

    /**
     * Tampilan pagawai.
     */
    public function index()
    {
        return view('laporan.payment-cs.index', [
            'title' => 'Laporan Payment CS',
            'users' => User::where('level_user', 4)->get(),
            'cs' => User::where('level_user', 3)->get(),
        ]);
    }

    public function data()
    {
        $filter = [];

        if (request()->has('timCs')) {
            $filter['timCs'] = request()->get('timCs');
        }
        if (request()->has('bulanSearch')) {
            $bulanSearch = request()->get('bulanSearch');
        }
        $data = $this->paymentCSService->grid($bulanSearch, $filter);
        return DataTables::of($data)
            ->addIndexColumn()
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'levelUser' => LevelUser::toArray(),
            'isEditing' => false
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(PegawaiRequest $request)
    {
        $this->paymentCSService->store($request->validated());
        return response()->json(['message' => 'Data berhasil disimpan'], 200);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $user = $this->paymentCSService->show($id);
        return view('data-master.pegawai.modal', [
            'title' => 'Pegawai',
            'pegawai' => $user,
            'levelUser' => LevelUser::toArray(),
            'isEditing' => true
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(PegawaiRequest $request, string $id)
    {
        $this->paymentCSService->update($id, $request->validated());
        return response()->json(['message' => 'Data berhasil diubah'], 200);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->paymentCSService->delete($id);
        return response()->json(['message' => 'Data berhasil dihapus'], 200);
    }

    function getDataBySales()
    {
        if (in_array(Auth::user()->level_user, [1, 2])) {
            $bulanSearch = request()->get('bulanSearch') ?? date('Y-m');
            // $timCs = request()->get('timCs') ?? Auth::user()->name;

            // if ($timCs === null) {
            //     // Handle the case where $timCs is null
            //     // For example, you can return an error response
            //     return response()->json(['error' => 'timCs is required'], 400);
            // }

            $orderCS = Order::select('id', 'order_id', 'product_code', 'product', 'created_at', 'status_shipment', 'status', 'net_revenue')
                // ->where('handled_by', $timCs)
                ->whereMonth('created_at', date('m', strtotime($bulanSearch)))
                ->whereYear('created_at', date('Y', strtotime($bulanSearch)))
                ->get();
            $orderCS = $orderCS->unique('order_id');
            $retur = Retur::with('order')->whereMonth('tanggal_retur', date('m', strtotime($bulanSearch)))
                ->whereYear('tanggal_retur', date('Y', strtotime($bulanSearch)))
                // ->whereRelation('order', function ($query)  {
                //     $query->where('handled_by', $timCs);
                // })
                // ->whereRelation('order', function ($query) use ($timCs) {
                //     $query->where('handled_by', $timCs);
                // })
                ->get()->count();
            $data = [
                'delivered' => $orderCS->where('status', 'completed')->where('status_shipment', 'finished_packages')->count() ?? 0,
                'close_order' => $orderCS->where('status', 'completed')->count() ?? 0,
                'close_rate' => $orderCS->count() > 0 ? ($orderCS->where('status', 'completed')->count() / $orderCS->count()) * 100 : 0,
                'retur' => $retur,
            ];
        }else{

            $bulanSearch = request()->get('bulanSearch') ?? date('Y-m');
            $timCs = request()->get('timCs') ?? Auth::user()->name;

            if ($timCs === null) {
                // Handle the case where $timCs is null
                // For example, you can return an error response
                return response()->json(['error' => 'timCs is required'], 400);
            }

            $orderCS = Order::select('id', 'order_id', 'product_code', 'product', 'created_at', 'status_shipment', 'status', 'net_revenue')
                ->where('handled_by', $timCs)
                ->whereMonth('created_at', date('m', strtotime($bulanSearch)))
                ->whereYear('created_at', date('Y', strtotime($bulanSearch)))
                ->get();
            $orderCS = $orderCS->unique('order_id');
            $retur = Retur::with('order')->whereMonth('tanggal_retur', date('m', strtotime($bulanSearch)))
                ->whereYear('tanggal_retur', date('Y', strtotime($bulanSearch)))
                // ->whereRelation('order', function ($query)  {
                //     $query->where('handled_by', $timCs);
                // })
                ->whereRelation('order', function ($query) use ($timCs) {
                    $query->where('handled_by', $timCs);
                })
                ->get()->count();
            $data = [
                'delivered' => $orderCS->where('status', 'completed')->where('status_shipment', 'finished_packages')->count() ?? 0,
                'close_order' => $orderCS->where('status', 'completed')->count() ?? 0,
                'close_rate' => $orderCS->count() > 0 ? ($orderCS->where('status', 'completed')->count() / $orderCS->count()) * 100 : 0,
                'retur' => $retur,
            ];
        }
        return response()->json($data);
    }
}
